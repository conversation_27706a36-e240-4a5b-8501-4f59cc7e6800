pluginManagement {
  val kotlinVersion: String by settings
  val quarkusVersion: String by settings
  val komapperVersion: String by settings
  val kspVersion: String by settings
  plugins {
    id("org.jetbrains.kotlin.jvm") version kotlinVersion
    id("io.quarkus") version quarkusVersion
    id("org.jetbrains.kotlin.plugin.allopen") version kotlinVersion
    id("org.komapper.gradle") version komapperVersion
    id("com.google.devtools.ksp") version "$kotlinVersion-$kspVersion"

  }
}

rootProject.name = "loms-app"

include(
  "bootstrap",
  "core:presentation",
  "core:application",
  "core:domain",
  "core:infrastructure:db",
  "core:infrastructure:planning"
)
