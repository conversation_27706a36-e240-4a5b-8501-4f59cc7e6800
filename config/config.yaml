http:
  hostname: ""
  port: 8080
  readTimeout: "5s"
  writeTimeout: "10s"
  idleTimeout: "2m"
  tls:
    enabled: false
    certificate: ""
    key: ""

app:
  name: "LOMS"
  environment: "local"
  # Change this on any live environments
  encryptionKey: "?E(G+KbPeShVmYq3t6w9z$C&F)J@McQf"
  timeout: "20s"
  passwordToken:
    expiration: "60m"
    length: 64
  emailVerificationTokenExpiration: "12h"
  solverApiUrl: 'http://localhost:8080'
  googleMapsBaseUrl: 'https://maps.googleapis.com/maps'
  googleMapsApiKey: 'AIzaSyCHJ-li-6v1GKKOncByczKMVMb5CGdck6Y'
  NavitimeApiUrl: 'https://api-service.navitime.biz'
  NavitimeCustomerId: 'r2300730'
  NavitimeSignature: 'df2df09b909e6435ff95a71259372e9e2c0524710853681cc4dc8bf6e5bbe4f4'
  NavitimeRequestCode: '1qazxsw20909'
  NavitimeToBeLoadedFrom: 'localhost:8081'

cache:
  capacity: 100000
  expiration:
    staticFile: "4380h"
    page: "24h"

database:
  driver: "sqlite3"
  migrateOnStartup: false
  logSQL: true
  connection: "dbs/loms_20250528_2.db?_journal=WAL&_timeout=5000&_fk=true"
  testConnection: ":memory:?_journal=WAL&_timeout=5000&_fk=true"

tasks:
  goroutines: 1
  releaseAfter: "15m"
  cleanupInterval: "1h"

mail:
  hostname: "localhost"
  port: 25
  user: "admin"
  password: "admin"
  fromAddress: "admin@localhost"
