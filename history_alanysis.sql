
-- complete year
SELECT driver_cd, snm, vehicle_cd, item_cd, pickup_cd, dest_cd, COUNT(*) AS count
FROM v_assign
WHERE g_no = 1 AND delete_flg = false AND ad_no IS NOT NULL AND assign_date BETWEEN '2024-01-01' AND '2025-01-01'
  -- AND quit_flg = false
  AND order_id IS NOT NULL AND vehicle_cd IS NOT NULL
GROUP BY driver_cd, snm, vehicle_cd, item_cd
       , pickup_cd, dest_cd
having count(*) > 1
ORDER BY count DESC, driver_cd, snm, vehicle_cd, item_cd
       , pickup_cd, dest_cd;

SELECT driver_cd, snm, vehicle_cd, item_cd, pickup_cd, dest_cd, COUNT(*) AS count
FROM v_assign
WHERE g_no = 1 AND delete_flg = false AND ad_no IS NOT NULL AND assign_date BETWEEN '2024-01-01' AND '2025-01-01'
  -- AND quit_flg = false
  AND order_id IS NOT NULL AND vehicle_cd IS NOT NULL
GROUP BY driver_cd, snm, vehicle_cd, item_cd
       , pickup_cd, dest_cd
having count(*) > 1
ORDER BY driver_cd, snm, vehicle_cd, item_cd, count DESC
       , pickup_cd, dest_cd;



-- monthly
SELECT TO_CHAR(assign_date, 'YYYY-MM') as month, driver_cd, snm, vehicle_cd, item_cd, pickup_cd, dest_cd, COUNT(*) AS count
FROM v_assign
WHERE g_no = 1 AND delete_flg = false AND ad_no IS NOT NULL AND assign_date BETWEEN '2024-01-01' AND '2025-01-01'
  -- AND quit_flg = false
  AND order_id IS NOT NULL AND vehicle_cd IS NOT NULL
GROUP BY month, driver_cd, snm, vehicle_cd, item_cd
       , pickup_cd, dest_cd
having count(*) > 1
ORDER BY month, count DESC, driver_cd, snm, vehicle_cd, item_cd
       , pickup_cd, dest_cd;

SELECT TO_CHAR(assign_date, 'YYYY-MM') as month, driver_cd, snm, vehicle_cd, item_cd, pickup_cd, dest_cd, COUNT(*) AS count
FROM v_assign
WHERE g_no = 1 AND delete_flg = false AND ad_no IS NOT NULL AND assign_date BETWEEN '2024-01-01' AND '2025-01-01'
  -- AND quit_flg = false
  AND order_id IS NOT NULL AND vehicle_cd IS NOT NULL
GROUP BY month, driver_cd, snm, vehicle_cd, item_cd
       , pickup_cd, dest_cd
having count(*) > 1
ORDER BY month, driver_cd, snm, vehicle_cd, item_cd, count DESC
       , pickup_cd, dest_cd;



-- complete year
SELECT driver_cd, snm, vehicle_cd, item_cd,
       -- pickup_cd, dest_cd,
       COUNT(*) AS count
FROM v_assign
WHERE g_no = 1 AND delete_flg = false AND ad_no IS NOT NULL AND assign_date BETWEEN '2024-01-01' AND '2025-01-01'
  -- AND quit_flg = false
  AND order_id IS NOT NULL AND vehicle_cd IS NOT NULL
GROUP BY driver_cd, snm, vehicle_cd, item_cd
       -- , pickup_cd, dest_cd
having count(*) > 1
ORDER BY count DESC, driver_cd, snm, vehicle_cd, item_cd;
       -- , pickup_cd, dest_cd;

SELECT driver_cd, snm, vehicle_cd, item_cd,
       -- pickup_cd, dest_cd,
       COUNT(*) AS count
FROM v_assign
WHERE g_no = 1 AND delete_flg = false AND ad_no IS NOT NULL AND assign_date BETWEEN '2024-01-01' AND '2025-01-01'
  -- AND quit_flg = false
  AND order_id IS NOT NULL AND vehicle_cd IS NOT NULL
GROUP BY driver_cd, snm, vehicle_cd, item_cd
       --, pickup_cd, dest_cd
having count(*) > 1
ORDER BY driver_cd, snm, vehicle_cd, item_cd, count DESC;
       --, pickup_cd, dest_cd;



-- monthly
SELECT TO_CHAR(assign_date, 'YYYY-MM') as month, driver_cd, snm, vehicle_cd, item_cd, pickup_cd, dest_cd, COUNT(*) AS count
FROM v_assign
WHERE g_no = 1 AND delete_flg = false AND ad_no IS NOT NULL AND assign_date BETWEEN '2024-01-01' AND '2025-01-01'
  -- AND quit_flg = false
  AND order_id IS NOT NULL AND vehicle_cd IS NOT NULL
GROUP BY month, driver_cd, snm, vehicle_cd, item_cd
       , pickup_cd, dest_cd
having count(*) > 1
ORDER BY month, count DESC, driver_cd, snm, vehicle_cd, item_cd
       , pickup_cd, dest_cd;

SELECT TO_CHAR(assign_date, 'YYYY-MM') as month, driver_cd, snm, vehicle_cd, item_cd, pickup_cd, dest_cd, COUNT(*) AS count
FROM v_assign
WHERE g_no = 1 AND delete_flg = false AND ad_no IS NOT NULL AND assign_date BETWEEN '2024-01-01' AND '2025-01-01'
  -- AND quit_flg = false
  AND order_id IS NOT NULL AND vehicle_cd IS NOT NULL
GROUP BY month, driver_cd, snm, vehicle_cd, item_cd
       , pickup_cd, dest_cd
having count(*) > 1
ORDER BY month, driver_cd, snm, vehicle_cd, item_cd, count DESC
       , pickup_cd, dest_cd;



select
    vehicle_cd || '~~~' || shipper_cd || '~~~' || pickup_cd || '~~~' || dest_cd || '~~~' || item_cd || '~~~' || driver_name  as pattern,
    count(cnt)
from v_order o
         left join m_params p on o.unit1 = p.p_id and p.p_cat = 'unit'
         left join (select distinct order_id, vehicle_cd from t_assign_detail
                    where order_id in
                          (select ad.order_id
                           from t_assign_detail ad
                                    left join t_order o on ad.order_id = o.order_id
                                    LEFT JOIN v_destination vd ON o.dest_cd = vd.dest_cd
                           where ad.order_id is not null
                             and vehicle_cd is not null
                             and deli_date between '2024-01-01' and '2025-01-01'
                             and g_no = 1
                             and o.order_status = 1
                             and o.delete_flg = false
                           group by ad.order_id
                           having count(distinct vehicle_cd) = 1)

                      and order_id is not null and vehicle_cd is not null
         ) v_assign on o.order_id = v_assign.order_id
where g_no = 1

  and o.delete_flg = false
  and vehicle_cd is not null -- because there are orders with two vehicles (those are excluded)
  and deli_date between '2024-01-01' and '2025-01-01'
  and order_status = 1
-- and vehicle_cd || '~~~' || shipper_cd || '~~~' || pickup_cd || '~~~' || dest_cd || '~~~' || item_cd || '~~~' || driver_name ='{00307~~~03101~~~00119~~~0000003267~~~00001~~~,武田,山下}'
group by pattern
order by 2 desc
;


select to_char(deli_date, 'YYYY-MM') as month,
       cnt,
       coalesce(set_time, 'なし') as                指定時間,
       shipper_snm as 荷主,
       pickup_nm as 積地,
       item_snm as 商品,
       coalesce(volume1, '') || p.p_value as 数量,

       coalesce(snm1, nm, '') || '(' || coalesce(spot, '') || ')' 届先,
       cmt1 社外記事,

       driver_name 乗務員,
       vehicle_cd 車両
from v_order o
         left join m_params p on o.unit1 = p.p_id and p.p_cat = 'unit'

         left join (select distinct order_id, vehicle_cd from t_assign_detail where order_id in (select ad.order_id
                                                                                                 from t_assign_detail ad
                                                                                                          left join t_order o on ad.order_id = o.order_id
                                                                                                          LEFT JOIN v_destination vd ON o.dest_cd = vd.dest_cd
                                                                                                 where ad.order_id is not null
                                                                                                   and vehicle_cd is not null
                                                                                                   and deli_date between '2024-01-01' and '2025-01-01'
                                                                                                   and g_no = 1
                                                                                                   and o.order_status = 1
                                                                                                   and o.delete_flg = false
                                                                                                 group by ad.order_id
                                                                                                 having count(distinct vehicle_cd) = 1)

                                                                                and order_id is not null and vehicle_cd is not null) v_assign on o.order_id = v_assign.order_id
where g_no = 1

  and o.delete_flg = false
  and vehicle_cd is not null -- because there are orders with two vehicles (those are excluded)
  and deli_date between '2024-01-01' and '2025-01-01'
  and order_status = 1
ORDER BY cnt desc, month, item_cd, shipper_cd, dest_cd, o.order_id
;

