quarkus.datasource.db-kind=mariadb
quarkus.datasource.username=root
quarkus.datasource.password=
#quarkus.datasource.jdbc.url=jdbc:h2:~/db/loms_db;AUTO_SERVER=TRUE
# for server based h2
quarkus.flyway.locations=db/migration
quarkus.flyway.migrate-at-start=true
quarkus.live-reload.instrumentation=true
quarkus.log.file.enable=true
quarkus.log.category."org.komapper.Sql".min-level=INFO
quarkus.log.category."org.komapper.Sql".level=INFO
quarkus.log.category."org.komapper.SqlWithArgs".min-level=TRACE
quarkus.log.category."org.komapper.SqlWithArgs".level=TRACE
quarkus.http.port=8081
quarkus.rest-client."com.sd.loms.infrastructure.planning.adapter.LomsSolverExternalService".url=http://localhost:8080
quarkus.rest-client."com.sd.loms.infrastructure.planning.adapter.LomsSolverExternalService".scope=jakarta.inject.Singleton

quarkus.rest-client."com.sd.loms.infrastructure.planning.adapter.NavitimeExternalService".url=https://trial.api-service.navitime.biz
quarkus.rest-client."com.sd.loms.infrastructure.planning.adapter.NavitimeExternalService".scope=jakarta.inject.Singleton
quarkus.rest-client."com.sd.loms.infrastructure.data.adapter.GoogleMapsExternalService".url=https://maps.googleapis.com/maps
quarkus.rest-client."com.sd.loms.infrastructure.data.adapter.GoogleMapsExternalService".scope=jakarta.inject.Singleton
quarkus.rest-client.logging.scope=request-response
quarkus.rest-client.logging.body-limit=5000

quarkus.log.category."org.jboss.resteasy.reactive.client.logging".level=DEBUG
quarkus.csrf-reactive.enabled=false
quarkus.locales=en,ja
quarkus.default-locale=en
quarkus.web-bundler.dependencies.compile-only=false
