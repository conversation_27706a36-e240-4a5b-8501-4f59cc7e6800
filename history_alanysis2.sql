
-- complete year
SELECT a.driver_cd, a.snm, a.vehicle_cd, a.item_cd, i.nm, a.volume1, a.unit1, pms.p_value unit_name,
       a.pickup_cd, p.nm, p.add1, p.add2, p.add3,
       a.dest_cd, dg.shipper_cd, s.nm as shipper_nm, ds.nm, ds.snm1, ds.snm2, da.nm, da.add1, da.add2,da.add3, COUNT(*) AS count
FROM v_assign a
    left join m_params pms on pms.p_cat='unit' and a.unit1 = pms.p_id
left join m_drivers d on a.driver_cd = d.driver_cd
left join m_items i on a.item_cd = i.item_cd
left join m_pickup p on a.pickup_cd = p.pickup_cd
left join m_dest_group dg on a.dest_cd = dg.dest_cd
left join m_shipper s on dg.shipper_cd = s.shipper_cd
left join m_dest_spot ds on dg.ds_no = ds.ds_no
left join m_dest_area da on ds.da_no = da.da_no
WHERE a.g_no = 1 AND a.delete_flg = false AND ad_no IS NOT NULL
  AND assign_date BETWEEN '2024-01-01' AND '2025-01-01'
  -- AND d.quit_flg = false
  AND order_id IS NOT NULL AND a.vehicle_cd IS NOT NULL
GROUP BY a.driver_cd, a.snm, a.vehicle_cd, a.item_cd, i.nm, a.volume1, a.unit1, unit_name
       , a.pickup_cd,p.nm, p.add1, p.add2, p.add3, a.dest_cd, dg.shipper_cd, shipper_nm
       , ds.nm, ds.snm1, ds.snm2
       , da.nm, da.add1, da.add2,da.add3
having count(*) > 1
ORDER BY count DESC, driver_cd, snm, vehicle_cd, item_cd, a.volume1, a.unit1, unit_name
       , pickup_cd, dest_cd, shipper_cd;




-- complete year
SELECT a.driver_cd, a.snm, a.vehicle_cd, a.item_cd, i.nm, a.volume1, a.unit1, pms.p_value unit_name, a.volume1 || ' ' || pms.p_value as valume,
       a.pickup_cd || coalesce(p.nm, '~') || coalesce(p.add1, '~') || coalesce(p.add2,'~')|| coalesce(p.add3, '~') as pickup,
       a.dest_cd || coalesce(s.nm, '~') || coalesce(ds.nm, '~') || coalesce(da.nm,'~') || coalesce(da.add1, '~') || coalesce(da.add2,'~')|| coalesce(da.add3, '~')as destination,
       a.pickup_cd, p.nm, p.add1, p.add2, p.add3,
       a.dest_cd, dg.shipper_cd, s.nm as shipper_nm, ds.nm, ds.snm1, ds.snm2, da.nm, da.add1, da.add2,da.add3,
       COUNT(*) AS count
FROM v_assign a
         left join m_params pms on pms.p_cat='unit' and a.unit1 = pms.p_id
         left join m_drivers d on a.driver_cd = d.driver_cd
         left join m_items i on a.item_cd = i.item_cd
         left join m_pickup p on a.pickup_cd = p.pickup_cd
         left join m_dest_group dg on a.dest_cd = dg.dest_cd
         left join m_shipper s on dg.shipper_cd = s.shipper_cd
         left join m_dest_spot ds on dg.ds_no = ds.ds_no
         left join m_dest_area da on ds.da_no = da.da_no
WHERE a.g_no = 1 AND a.delete_flg = false AND ad_no IS NOT NULL
  AND assign_date BETWEEN '2024-01-01' AND '2025-01-01'
  -- AND d.quit_flg = false
  AND order_id IS NOT NULL AND a.vehicle_cd IS NOT NULL
GROUP BY a.driver_cd, a.snm, a.vehicle_cd, a.item_cd, i.nm, a.volume1, a.unit1, pms.p_value
       , a.pickup_cd,p.nm, p.add1, p.add2, p.add3, a.dest_cd, dg.shipper_cd, s.nm
       , ds.nm, ds.snm1, ds.snm2
       , da.nm, da.add1, da.add2,da.add3
having count(*) > 1
ORDER BY count DESC, driver_cd, snm, vehicle_cd, item_cd, a.volume1, a.unit1, pms.p_value
       , pickup_cd, dest_cd, shipper_cd;
