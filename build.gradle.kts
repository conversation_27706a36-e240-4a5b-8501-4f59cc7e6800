plugins {
	kotlin("jvm") version "2.1.0"
	kotlin("plugin.allopen") version "2.1.0"
	kotlin("plugin.noarg") version "2.1.0"
	id("io.quarkus")
}

repositories {
	mavenCentral()
	mavenLocal()
}

val quarkusPlatformGroupId: String by project
val quarkusPlatformArtifactId: String by project
val quarkusPlatformVersion: String by project
val quarkusRenardeVersion: String by project
val quarkusWebBuilderVersion: String by project

val timefoldVersion: String by project

dependencies {
	implementation(enforcedPlatform("${quarkusPlatformGroupId}:${quarkusPlatformArtifactId}:${quarkusPlatformVersion}"))
	implementation("io.quarkus:quarkus-rest")
	implementation("io.quarkus:quarkus-rest-client")
	implementation("io.quarkus:quarkus-rest-jackson")
	implementation("io.quarkus:quarkus-rest-client-jackson")
	implementation("io.quarkus:quarkus-kotlin")
	implementation("ai.timefold.solver:timefold-solver-quarkus:${timefoldVersion}")
	implementation("ai.timefold.solver:timefold-solver-quarkus-jackson:${timefoldVersion}")
	implementation("org.apache.commons:commons-lang3")
	implementation("org.jetbrains.kotlin:kotlin-stdlib-jdk8")
	implementation("io.quarkus:quarkus-arc")
	testImplementation("io.quarkus:quarkus-junit5")
	testImplementation("io.rest-assured:rest-assured")


	implementation("io.quarkiverse.renarde:quarkus-renarde:${quarkusRenardeVersion}")
	implementation("io.quarkiverse.renarde:quarkus-renarde-security:${quarkusRenardeVersion}")
	implementation("io.quarkus:quarkus-hibernate-orm-panache-kotlin")
	implementation("io.quarkiverse.jdbc:quarkus-jdbc-sqlite:3.0.11")
	implementation("io.quarkiverse.web-bundler:quarkus-web-bundler:${quarkusWebBuilderVersion}")

	implementation("org.mvnpm:unpoly:3.9.3")
	implementation("org.mvnpm:alpinejs:3.14.9")
	implementation("org.mvnpm.at.alpinejs:persist:3.14.9")

	implementation("org.mvnpm:admin-lte:4.0.0-beta2")
	implementation("org.mvnpm.at.popperjs:core:2.11.8")
	implementation("org.mvnpm:bootstrap:5.3.7")
	implementation("org.mvnpm:bootstrap-icons:1.13.1")
	implementation("org.mvnpm:jquery:3.7.1")
	implementation("org.mvnpm:tom-select:2.4.3")
	implementation("org.mvnpm:sweetalert2:11.22.1")

	// implementation("org.mvnpm:vis-timeline:7.7.4")
}

java {
	sourceCompatibility = JavaVersion.VERSION_21
	targetCompatibility = JavaVersion.VERSION_21
}

tasks.withType<Test> {
	systemProperty("java.util.logging.manager", "org.jboss.logmanager.LogManager")
}
allOpen {
	annotation("jakarta.ws.rs.Path")
	annotation("jakarta.enterprise.context.ApplicationScoped")
	annotation("jakarta.persistence.Entity")
	annotation("io.quarkus.test.junit.QuarkusTest")
}

noArg {
	annotation("ai.timefold.solver.core.api.domain.solution.PlanningSolution")
    annotation("ai.timefold.solver.core.api.domain.entity.PlanningEntity")
}

tasks.withType<org.jetbrains.kotlin.gradle.tasks.KotlinCompile> {
	kotlinOptions.jvmTarget = JavaVersion.VERSION_21.toString()
	kotlinOptions.javaParameters = true
}
