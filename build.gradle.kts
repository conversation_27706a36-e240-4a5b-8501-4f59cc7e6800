plugins {
  kotlin("jvm") version "2.0.10"
  id("java")
}

repositories {
  mavenLocal()
  mavenCentral()
}

subprojects {
  tasks.withType<org.jetbrains.kotlin.gradle.tasks.KotlinCompile> {
    kotlinOptions.jvmTarget = JavaVersion.VERSION_21.toString()
    kotlinOptions.javaParameters = true
  }

  repositories {
    mavenLocal()
    mavenCentral()
  }

  apply {
    plugin("kotlin")
  }


  val quarkusVersion: String by project
  val cdiApiVersion: String by project

  dependencies {
    implementation(enforcedPlatform("io.quarkus.platform:quarkus-bom:${quarkusVersion}"))
    implementation("jakarta.enterprise:jakarta.enterprise.cdi-api:${cdiApiVersion}")
  }
}
