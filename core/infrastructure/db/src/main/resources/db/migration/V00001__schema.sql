CREATE TABLE allocation_run
(
  ID        BIGINT AUTO_INCREMENT PRIMARY KEY,
  DATE_TIME TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  STATUS    SMALLINT
);

CREATE TABLE solution
(
  ID          BIGINT AUTO_INCREMENT PRIMARY KEY,
  RUN_ID      BIGINT NOT NULL,
  D<PERSON>E_TIME   TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  TOTAL_TIME  BIGINT,
  VISITS      VARCHAR(255),
  SCORE       VARCHAR(255),
  IS_FEASIBLE INT,
  FOREIGN KEY (RUN_ID) REFERENCES allocation_run (ID)
);

CREATE TABLE material_category
(
  ID   BIGINT AUTO_INCREMENT PRIMARY KEY,
  NAME VARCHAR(255) UNIQUE
);

CREATE TABLE location
(
  ID               BIGINT AUTO_INCREMENT PRIMARY KEY,
  LOCATION_TYPE    INTEGER,
  DESCRIPTION      VARCHAR(255) UNIQUE,
  IS_DEPOT         BOOLEAN         NOT NULL,
  LAT              DECIMAL(17, 14) NOT NULL,
  <PERSON><PERSON>              DECIMAL(17, 14) NOT NULL,
  READY_TIME       BIGINT          NOT NULL,
  DUE_TIME         BIGINT          NOT NULL,
  SERVICE_DURATION BIGINT          NOT NULL
);

CREATE TABLE distance
(
  FROM_LOC BIGINT NOT NULL,
  TO_LOC   BIGINT NOT NULL,
  DISTANCE BIGINT NOT NULL,
  PRIMARY KEY (FROM_LOC, TO_LOC),
  FOREIGN KEY (FROM_LOC) REFERENCES location (ID),
  FOREIGN KEY (TO_LOC) REFERENCES location (ID)
);

CREATE TABLE job
(
  ID                BIGINT AUTO_INCREMENT PRIMARY KEY,
  PICKUP            BIGINT NOT NULL,
  DELIVERY          BIGINT NOT NULL,
  MATERIAL_CATEGORY BIGINT NOT NULL,
  FOREIGN KEY (PICKUP) REFERENCES location (ID),
  FOREIGN KEY (DELIVERY) REFERENCES location (ID),
  FOREIGN KEY (MATERIAL_CATEGORY) REFERENCES material_category (ID)
);

CREATE TABLE driver
(
  ID   BIGINT AUTO_INCREMENT PRIMARY KEY,
  NAME VARCHAR(255) UNIQUE
);

CREATE TABLE vehicle
(
  ID              BIGINT AUTO_INCREMENT PRIMARY KEY,
  NAME            VARCHAR(255) UNIQUE,
  COLOR           VARCHAR(255),
  CAPACITY        DECIMAL(10, 2) NOT NULL,
  CAPACITY_LITERS DECIMAL(10, 2) NOT NULL
);

CREATE TABLE driver_material_category
(
  DRIVER            BIGINT NOT NULL,
  MATERIAL_CATEGORY BIGINT NOT NULL,
  PRIMARY KEY (DRIVER, MATERIAL_CATEGORY),
  FOREIGN KEY (DRIVER) REFERENCES driver (ID),
  FOREIGN KEY (MATERIAL_CATEGORY) REFERENCES material_category (ID)
);

CREATE TABLE vehicle_material_category
(
  VEHICLE           BIGINT NOT NULL,
  MATERIAL_CATEGORY BIGINT NOT NULL,
  PRIMARY KEY (VEHICLE, MATERIAL_CATEGORY),
  FOREIGN KEY (VEHICLE) REFERENCES vehicle (ID),
  FOREIGN KEY (MATERIAL_CATEGORY) REFERENCES material_category (ID)
);
