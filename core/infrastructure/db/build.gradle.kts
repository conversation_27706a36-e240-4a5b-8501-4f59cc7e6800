plugins {
  id("org.kordamp.gradle.jandex") version "1.0.0"
  id("com.google.devtools.ksp")
  id("org.komapper.gradle")
  kotlin("plugin.allopen")
}


repositories {
  mavenCentral()
  mavenLocal()
  maven {
    url = uri("https://repo.mvnpm.org/maven2")
  }
}


val komapperVersion: String by project
dependencies {
  platform("org.komapper:komapper-platform:$komapperVersion").let {
    implementation(it)
    ksp(it)
  }
  implementation(project(":core:domain"))

  implementation("io.quarkus:quarkus-kotlin")
  implementation("io.quarkus:quarkus-rest-client-reactive-jackson")
  implementation("io.quarkus:quarkus-jdbc-mariadb")
  implementation("io.quarkus:quarkus-flyway")
  implementation("org.flywaydb:flyway-mysql")
  implementation("org.komapper:komapper-quarkus-jdbc")
  implementation("org.komapper:komapper-dialect-mariadb-jdbc")
  runtimeOnly("org.komapper:komapper-slf4j")
  ksp("org.komapper:komapper-processor")
}

ksp {
  arg("komapper.namingStrategy", "UPPER_SNAKE_CASE")
}

allOpen {
  annotation("jakarta.ws.rs.Path")
  annotation("jakarta.enterprise.context.ApplicationScoped")
  annotation("io.quarkus.test.junit.QuarkusTest")
}

