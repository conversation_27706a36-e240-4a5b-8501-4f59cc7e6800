import persist from '@alpinejs/persist'
import '@popperjs/core'
import 'bootstrap/dist/js/bootstrap.min.js';
import "adminlte4/dist/js/adminlte.min.js";
import Alpine from 'alpinejs';

import './map'

window.Alpine = Alpine
Alpine.plugin(persist)
Alpine.start()

import $ from 'jquery/dist/jquery.min.js';
import Swal from 'sweetalert2/dist/sweetalert2.all.min.js';
import TomSelect from 'tom-select/dist/js/tom-select.complete.min.js';
import 'unpoly/unpoly.min.js';


up.fragment.config.mainTargets.push('main')
up.feedback.config.currentClasses = ['active']
up.link.config.followSelectors.push('a[href]');
up.form.config.submitSelectors.push(['form']);
up.fragment.config.navigateOptions.transition = 'cross-fade';
up.fragment.config.navigateOptions.cache = false;
if (window.location.hostname.toLowerCase() === 'localhost') {
  up.log.enable();
} else {
  up.log.disable();
}
up.compiler('#map', function (element) {
  initMap()
});

let Toast = Swal.mixin({
  toast: true,
  position: 'top-end',
  showConfirmButton: false,
  timer: 3000
});

up.compiler('.toast', function (element, data) {
  Toast.fire({
    icon: data.type,
    title: data.text
  })
});

up.compiler('.tom-select', function (element) {
  new TomSelect(element, {plugins:['remove_button','dropdown_input']});
})

up.on('app:map-loaded', function(event) {
  console.log(event)
  $('#map-loading').slideUp();
});

up.on('up:fragment:inserted', (event, fragment) => {
  fragment.classList.add('new-fragment', 'inserted')
  up.util.timer(0, () => fragment.classList.remove('inserted'))
  up.util.timer(1000, () => fragment.classList.remove('new-fragment'))
});

window.reload = ()=> {
  // TODO commented the SPA like reload due to css issues, need to fix
  // up.reload({ target: 'body', url: window.location.href })
  window.location.reload();
}


const initMap = function () {
  if (window.mapscript) {
    try {
      window.map = new mapscript.Map('m2302220', {
        target: '#map',
        center: new mapscript.value.LatLng(34.72383109685586, 135.45281896187524),
        zoomLevel: 9
      });
      window.map.setMapVectorCondition(new mapscript.value.MapVectorCondition({}));
      up.emit('app:map-loaded', {  })
    } catch (error) {
      console.error("Error initializing the map:", error);
    }
  } else {
    console.info("mapscript is not loaded or available, retrying after sometime");
    setTimeout(function () {
      initMap()
    }, 500)
  }
}
