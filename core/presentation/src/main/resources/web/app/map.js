import $ from 'jquery/dist/jquery.min.js';

window.pins = []
window.markers = []
window.routePath = []

window.tripDataAlpine = function () {
  return {
    visits: [],
    totalTime: '',
    selectedVehicle: 0,
    data: [],
    init() {
      this.data = up.data(document.querySelector("#data"));
    },
    onVehicleChange(vehicle) {
      this.selectedVehicle = vehicle;
      this.visits = [];
      this.totalTime = '';

      deleteMarkers();
      let selectedVehicleData = this.data.find(obj => obj.vehicle.id === this.selectedVehicle);
      console.log(selectedVehicleData);
      if(!selectedVehicleData){
        return;
      }
      addMarkersAndPaths(selectedVehicleData, this);
    }
  }
}


function addMarkersAndPaths(obj, alpineComponent) {
  for (const visit of obj.visits) {
    const info = new mapscript.value.GLMarkerIconInfo({
      icon: '/pin.png',
    });
    const position = new mapscript.value.LatLng(visit.lat, visit.lon);
    let marker = new mapscript.object.GLMarker({position, info});

    window.map.addGLMarker(marker);
    const infoWindow = new mapscript.object.InfoWindow({
      content: `
                  <h5>${visit.name}</h5> 到着時刻: ${visit.arrivalTime} <br> 
                  サービス期間: ${visit.serviceDuration} <br> 
                  出発時間: ${visit.departureTime}`, position
    });
    infoWindow.close();
    window.map.addInfoWindow(infoWindow);
    marker.addEventListener("click", () => {
      if (infoWindow.isOpen()) {
        infoWindow.close();
        return;
      }
      infoWindow.open();
    });
    window.markers.push(marker);
  }

  let wayPoints = [];
  for (let i = 1; i < obj.track.length - 1; i++) {
    wayPoints.push({lat: obj.track[i].lat, lon: obj.track[i].lon, name: obj.visits[i].name});
  }

  let now = new Date();
  let Year = now.getFullYear();
  let Month = ("0" + (now.getMonth() + 1)).slice(-2);
  let Day = ("0" + now.getDate()).slice(-2);

  let DateTime = Year + '-' + Month + '-' + Day + 'T09:00:00';

  // ルート線を消去します
  if (routePath.length !== 0) {
    for (let i = 0; i < routePath.length; i++) {
      routePath.forEach(function (r) {
        map.removeGeoJsonFigure(r);
      });
    }
    routePath = [];
  }
  // ピンを消去します
  if (window.pins.length !== 0) {
    for (let i = 0; i < window.pins.length; i++) {
      map.removeGLMarker(window.pins[i]);
    }
    window.pins = [];
  }

  let start = {'coord': {'lon': obj.track[0].lon, 'lat': obj.track[0].lat}, 'node': '', name: obj.visits[0].name},
    goal = {'coord': {'lon': obj.track[0].lon, 'lat': obj.track[0].lat}, 'node': '', name: obj.visits[0].name},
    params = {
      'start': JSON.stringify(createVertex(start)),
      'goal': JSON.stringify(createVertex(goal)), // 'goal': `${wayPoints[0].lat},${wayPoints[0].lon}`,
      'start_time': DateTime,
      'via': JSON.stringify(wayPoints),
      'signature': 'edaa164f6d01fb2f0b61e286c5c8ed2527791738b9e41679168aca880593f35e',
      'request_code': '1qazxsw20909',
      'host': 'localhost:8081',
      'shape': 'true', //'regulation': JSON.stringify({"regulation-type": "large_truck","toll-type": "large","car-body": {"length": 1200,"height": 320,"width": 250,"weight": 24860,"max-loading-capacity": 15300}})
    };
  let base_url = 'https://trial.api-service.navitime.biz/m2302220/v1'
  searchRoute(params, base_url + '/route_car', alpineComponent);
}


function deleteMarkers() {
  for (const marker of window.markers) {
    window.map.removeGLMarker(marker);
  }
  window.markers = [];


  for (const path of window.routePath) {
    window.map.removeGeoJsonFigure(path);
  }
  window.routePath = [];
}


function searchRoute(params, route_url, alpineComponent) {
  $.ajax({url: route_url, data: params, type: 'GET', dataType: 'jsonp', jsonp: 'callback', jsonpCallback: 'route',})
    .done(function (json) {
      if (json.items.length === 0) {
        console.log('no data');
        return;
      }

      window.sections = json.items[0].sections;
      tripData(window.sections, alpineComponent);
      let pinIndex = 0;
      window.sections.forEach(function (section, i) {
        if (section.type !== 'point' || i !== 0 && i !== sections.length - 1 && !section.with_via) {
          return true;
        }
        section.img = '/pin.png';
      });

      drawRouteSections();
      const figure = new mapscript.value.GeoJsonFigureCondition(json.items[0].shapes, {
        isRouteShape: true, showRouteArrow: true, coordUnit: "degree", polyline: {
          color: new mapscript.value.Color(0, 1, 0, 0)
        }
      });
      window.routePath.push(figure);
      const [lng1, lat1, lng2, lat2] = json.items[0].shapes.bbox;
      const rect = mapscript.util.locationsToLatLngRect([new mapscript.value.LatLng(lat1, lng1), new mapscript.value.LatLng(lat2, lng2)]);
      window.map.moveBasedOnLatLngRect(rect, true);
      window.map.addGeoJsonFigure(figure);
    })
    .fail(function (XMLHttpRequest, textStatus, errorThrown) {
      console.log("XMLHttpRequest : " + XMLHttpRequest.status);
      console.log("textStatus     : " + textStatus);
      console.log("errorThrown    : " + errorThrown.message);
    })
}

function tripData(data, alpineComponent) {
  const points = [];
  const moves = [];
  const visits = [];
  let currentTime = 32400000 // Set to 9 AM
  let totalTravelTime = 0;

  data.forEach(item => {
    if (item.type === 'point') {
      points.push(item);
    } else if (item.type === 'move') {
      moves.push(item);
      totalTravelTime += item.time;
    }
  });


  if (points.length > 0) {
    const startingPoint = points[0];
    visits.push({
      id: 1,
      name: startingPoint.name,
      arrivalTime: currentTime,
      departureTime: currentTime
    });
  }

  for (let i = 1; i < points.length; i++) {
    if (i - 1 < moves.length) {
      const point = points[i];
      const move = moves[i - 1];
      const moveTime = move.time;
      const arrivalTime = currentTime + (moveTime * 60000);
      const departureTime = arrivalTime + (20 * 60000); // Add 20 minutes wait time

      visits.push({
        id: i + 1,
        name: point.name,
        arrivalTime: arrivalTime,
        departureTime: departureTime
      });

      currentTime = departureTime;
    }
  }

  visits[visits.length - 1].departureTime = null;

  alpineComponent.totalTime = toTime(totalTravelTime * 60000);
  alpineComponent.visits = visits.map(value => {
    value.arrivalTime = toTime(value.arrivalTime)
    value.departureTime = toTime(value.departureTime)
    return value;
  });
}


function toTime(totalTime) {
  if (totalTime == null) {
    return '00:00';
  }
  const hours = Math.floor(totalTime / (1000 * 60 * 60));
  const minutes = Math.floor((totalTime % (1000 * 60 * 60)) / (1000 * 60));
  return String(hours).padStart(2, '0') + ':' + String(minutes).padStart(2, '0');
}

function createVertex(spot) {

  let vertex = {lon: spot.coord.lon, lat: spot.coord.lat, name: spot.name};
  if (spot.node) {
    vertex.node = spot.node;
  }
  if (spot.stay_time) {
    vertex['stay-time'] = spot.stay_time;
  }
  return vertex;
}


function drawRouteSections() {
  let parsed = [], tmp = {}, maxIndex = sections.length - 1;

  window.sections.forEach(function (section, i) {
    // point セクションに対する処理
    if (section.type === 'point') {
      // 経由地の場合は強調して色をつけます
      if (section.with_via) {
        tmp.name_style = 'font-weight:bold;';
        tmp.stopper_color = '#3cb371';
        tmp.line_style = 'display: show;';
      }
      // 画像がある場合はそれを設定します
      if (section.img) {
        tmp.img = section.img;
        tmp.show_weather = true;
        tmp.id = String(i);
        if (i != 0) {
          tmp.id = String(i / 2);
        }
      }
      tmp.point_name = section.name;  // 地点名
      // move セクションに対する処理
    } else {
      tmp.link_name = section.line_name;
      tmp.duration = '（' + section.time + '分）';                // 所要時間

      // セクションの出発時刻
      tmp.from_time = section.from_time;
      // セクションの到着時刻
      tmp.to_time = section.to_time;
    }

    // 起点の場合
    if (i === 0) {
      tmp.name_style = 'font-weight:bold';
      tmp.stopper_color = '#4169e1'
      // 終点の場合
    } else if (i === maxIndex) {
      tmp.name_style = 'font-weight:bold';
      tmp.stopper_color = '#cd5c5c';
      tmp.line_style = 'display: none;';
      parsed.push(tmp);
      // それ以外かつ move セクションだった場合
    } else if (section.type === 'move') {
      parsed.push(tmp);
      tmp = {};
    }
  });

}
