{#include page}
  {#title}{m:material_category} | {#if isEdit}{m:details}: {category.name}{#else}{m:create(m:material_category)}{/if}{/title}
  {#heading}{#if isEdit}{m:details}: {category.name}{#else}{m:create(m:material_category)}{/if}{/heading}
  {#formn "MaterialCategories" isEdit=isEdit id=category.id errors=errors up-layer="root"}
    {#field name="name" label=m:material_category value=category.name focus=1 errors=errors/}
  {/formn}
{/include}
