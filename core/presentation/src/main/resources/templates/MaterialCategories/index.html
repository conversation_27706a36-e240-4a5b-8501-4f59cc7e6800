{#include page}
  {#title}{m:material_category}{/title}
  {#heading}{m:material_category}{/heading}
  {#addLink type="MaterialCategories" /}
  {#let uri=uri:MaterialCategories.index() page=categories}
    <table class="table table-striped table-hover">
      <thead>
      <tr>
        {#sortable "Id" label=m:id uri=uri page=page/}
        {#sortable "Name" label=m:material_category uri=uri page=page/}
      </tr>
      </thead>
      <tbody>
      {#each category in categories}
        {#fragment id=row}
        <tr id="row_{category.id}">
          <td><a href="{uri:MaterialCategories.details(category.id)}" up-history="false" up-layer="new" up-on-dismissed="up.reload('.table')">{category.id}</a></td>
          <td><a href="{uri:MaterialCategories.details(category.id)}" up-history="false" up-layer="new" up-on-dismissed="up.reload('.table')">{category.name}</a></td>
        </tr>
        {/fragment}
      {/each}
      </tbody>
    </table>
    {#pagination page=page uri=uri/}
  {/let}
{/include}
