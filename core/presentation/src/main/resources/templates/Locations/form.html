{#include page}
  {#title}{m:location} | {#if isEdit}{m:details}: {location.description}{#else}{m:create(m:location)}{/if}{/title}
  {#heading}{#if isEdit}{m:details}: {location.description}{#else}{m:create(m:location)}{/if}{/heading}
  {#formn "Locations" isEdit=isEdit id=location.id errors=errors up-layer="root"}
    {#alert type="warning" message=message /}
    {#field name="description" label=m:location value=location.description focus=1 errors=errors/}

    <input type="hidden" name="romsId" value="{location.romsId}">
    {#field name="zip" label=m:zip value=location.zip errors=errors cols="6"/}
    {#field name="add1" label=m:add1 value=location.add1 errors=errors cols="6"/}
    {#field name="add2" label=m:add2 value=location.add2 errors=errors cols="6"/}
    {#field name="add3" label=m:add3 value=location.add3 errors=errors cols="6"/}

    {#field name="lat" label="Lattitude" value=location.lat cols="6" errors=errors/}
    {#field name="lon" label="Longitude" value=location.lon cols="6" errors=errors/}
    {#field name="readyTime" label="Ready Time" value=location.readyTime cols="4" type="number" errors=errors/}
    {#field name="dueTime" label="Due Time" value=location.dueTime cols="4" type="number" errors=errors/}
    {#field name="serviceDuration" label="Service Duration" value=location.serviceDuration cols="4" type="number" errors=errors/}
    <div class="col-6">
      {#checkbox name="locationType" label="PICKUP" value="0" checked=location.isPickup errors=errors/}
      {#checkbox name="locationType" label="DELIVERY" value="1" checked=location.isDelivery errors=errors/}
    </div>
    <div class="col-6">
      {#checkbox name="isDepot" label="Depot" value="true" type="checkbox" checked=location.isDepot errors=errors/}
    </div>
  {/formn}
{/include}
