{#include page}
  {#title}{m:location}{/title}
  {#heading}{m:location}{/heading}
  {#addLink type="Locations" /}
  {#importLink href=uri:Locations.csvImport() /}
  <br>
  <br>
  {#let uri=uri:Locations.index() page=locations}
    <table class="table table-striped table-hover">
      <thead>
      <tr>
        {#sortable "Id" label=m:id uri=uri page=page/}
        {#sortable "Description" label=m:location uri=uri page=page/}
        {#sortable "locationType" label=m:location uri=uri page=page/}
        <th>{m:add1}</th>
        <th>{m:add2}</th>
        <th>{m:add3}</th>
        <th>{m:zip}</th>
      </tr>
      </thead>
      <tbody>
      {#each location in locations}
        {#fragment id=row}
        <tr id="row_{location.id}">
          <td><a href="{uri:Locations.details(location.id)}" up-history="false" up-layer="new" up-on-dismissed="up.reload('.table')">{location.id}</a></td>
          <td><a href="{uri:Locations.details(location.id)}" up-history="false" up-layer="new" up-on-dismissed="up.reload('.table')">{location.description}</a></td>
          <td>{location.locType}</td>
          <td>{location.add1}</td>
          <td>{location.add2}</td>
          <td>{location.add3}</td>
          <td>{location.zip}</td>
        </tr>
        {/fragment}
      {/each}
      </tbody>
    </table>
    {#pagination page=page uri=uri/}
  {/let}
{/include}
