{#include page}
  <form action="{uri:Locations.upload()}" method="post" up-autosubmit>
    <br/>
    {#alert type="warning" message=message /}
    <div class="mb-3">
      <label for="file" class="form-label">{m:csv_file}</label>
      <input class="form-control" name="file" id="file" type="file">
    </div>

    {#if errors}
      <ul class="list-group list-group-numbered">
        {#each error in errors}
          <li class="list-group-item list-group-item-warning">{error}</li>
        {/each}
      </ul>
    {/if}
  </form>
{/include}
