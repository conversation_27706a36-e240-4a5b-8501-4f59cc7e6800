{#include page}
  {#title}{m:driver} | {#if isEdit}{m:details}: {driver.name}{#else}{m:create(m:driver)}{/if}{/title}
  {#heading}{#if isEdit}{m:details}: {driver.name}{#else}{m:create(m:driver)}{/if}{/heading}
  {#formn "Drivers" isEdit=isEdit id=driver.id errors=errors up-layer="root"}
    {#field name="name" label=m:driver value=driver.name focus=1 errors=errors/}
    <div class="col-md-12">
      <label for="canHandle">{m:can_handle}</label>
      <select name="canHandle" id="canHandle" class="form-control tom-select {#if errors && errors.get('name')} is-invalid{/if}" multiple>
        {#each materialCategories}
          <option value="{it.id}"{#if it.checked} selected{/if}>{it.name}</option>
        {/each}
      </select>
      {#if errors && errors.get('canHandle')}<div class="invalid-feedback">{errors.get('canHandle')}</div>{/if}
    </div>
  {/formn}
{/include}
