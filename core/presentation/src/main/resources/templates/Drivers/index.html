{#include page}
  {#title}{m:driver}{/title}
  {#heading}{m:driver}{/heading}
  {#addLink type="Drivers" /}
  {#let uri=uri:Drivers.index() page=drivers}
    <table class="table table-striped table-hover">
      <thead>
      <tr>
        {#sortable "Id" label=m:id uri=uri page=page/}
        {#sortable "Name" label=m:driver uri=uri page=page/}
        <th>{m:can_handle}</th>
      </tr>
      </thead>
      <tbody>
      {#each driver in drivers}
        {#fragment id=row}
        <tr id="row_{driver.id}">
          <td><a href="{uri:Drivers.details(driver.id)}" up-history="false" up-layer="new" up-on-dismissed="up.reload('.table')">{driver.id}</a></td>
          <td><a href="{uri:Drivers.details(driver.id)}" up-history="false" up-layer="new" up-on-dismissed="up.reload('.table')">{driver.name}</a></td>
          <td>{driver.canHandleNames}</td>
        </tr>
        {/fragment}
      {/each}
      </tbody>
    </table>
    {#pagination page=page uri=uri/}
  {/let}
{/include}
