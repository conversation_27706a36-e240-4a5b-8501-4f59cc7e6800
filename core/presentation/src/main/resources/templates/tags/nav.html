<nav class="app-header navbar navbar-expand bg-body">
  <div class="container-fluid">
    <ul class="navbar-nav">
      <li class="nav-item">
        <a class="nav-link" data-lte-toggle="sidebar" href="#" role="button">
          <i class="bi bi-list"></i>
        </a>
      </li>
    </ul>
    <div class="collapse navbar-collapse" id="navbarNavDarkDropdown">
      <ul class="navbar-nav ms-auto">
        <li class="nav-item dropdown">
          <a class="nav-link dropdown-toggle" href="#" id="navbarDarkDropdownMenuLink" role="button" data-bs-toggle="dropdown" aria-expanded="false">
            {inject:i18n.lang}
          </a>
          <ul class="dropdown-menu dropdown-menu-dark" aria-labelledby="navbarDarkDropdownMenuLink">
            {#if ! inject:i18n.isCurrentLang('en') }<li><a class="dropdown-item" href="{uri:Home.changeLanguage('en')}" up-history="false" up-on-finished="reload()">English</a></li>{/if}
            {#if ! inject:i18n.isCurrentLang('ja') }<li><a class="dropdown-item" href="{uri:Home.changeLanguage('ja')}" up-history="false" up-on-finished="reload()">日本語</a></li>{/if}
          </ul>
        </li>
      </ul>
    </div>
    <ul class="navbar-nav ms-auto">
      <li class="nav-item">
        <a class="nav-link" href="#" data-lte-toggle="fullscreen">
          <i data-lte-icon="maximize" class="bi bi-arrows-fullscreen" style="display: block;"></i>
          <i data-lte-icon="minimize" class="bi bi-fullscreen-exit" style="display: none;"></i>
        </a>
      </li>
      <li class="nav-item">
        <a class="nav-link">
          <i class="bi" :class="dark? 'bi-moon' : 'bi-sun'" @click="dark = !dark"></i>
        </a>
      </li>
      <li class="nav-item">
        <a class="nav-link" href="#" data-hx-get="/api/user/logout">
          <i class="bi bi-box-arrow-right"></i>
        </a>
      </li>
    </ul>
  </div>
</nav>
