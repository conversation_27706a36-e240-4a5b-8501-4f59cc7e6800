{#include page}
  {#title}{m:allocation_page_heading}{/title}
  {#heading}{m:allocation_page_heading}{/heading}

  <div class="card-group">
    <div class="card">
      <div class="card-header">
        <div class="card-title">Get Data</div>
      </div>
      <div class="card-body">
        <form class="row g-3" action="{uri:Solution.index()}">
          <div class="col-md-6 mb-3">
            <label for="date" class="form-label">Date</label>
            <input id="date" type="date" name="date" value="{date}" class="form-control" up-validate="#runs, #solutions, #solution-details">
          </div>
          <div class="col-md-6 mb-3" id="runs">
            <label for="runId" class="form-label">Runs: {runs.size}</label>
            <select name="runId" class="form-select" id="runId" up-validate="#solutions, #solution-details">
              <option value="0">Please Select</option>
              {#each runs}
                <option data-run-id="{it.id}" value="{it.id}">{it.id}</option>
              {/each}
            </select>
          </div>
          <div class="col-md-12 mb-3" id="solutions">
            <label for="solutionId" class="form-label">Solution: {solutions.size}</label>
            <select id="solutionId" name="solutionId" class="form-select" up-validate="#solution-details">
              <option value="0">Please Select</option>
              {#each solutions}
                <option data-run-id="{it.runId}" value="{it.id}">{it.readableTotalTime}, Score: {it.score}</option>
              {/each}
            </select>
          </div>
        </form>
      </div>
    </div>
    <div id="run-details" up-defer up-href="{uri:Solution.runDetails(date)}">
      Loading...
    </div>
  </div>

  <div class="row mt-5">
    <div class="col-md-12">
      <div class="card">
        <div class="card-header">
          <div class="card-title">Solution Details</div>
        </div>
        <div class="card-body">
          <div id="solution-details">
            {#if solutionId}
              <div id="actual-solution" up-defer up-href="{uri:Solution.solution(solutionId)}"></div>
            {/if}
          </div>
        </div>
      </div>
    </div>
  </div>
  <div id="map-container" up-defer up-href="{uri:Solution.map()}"></div>
{/include}
