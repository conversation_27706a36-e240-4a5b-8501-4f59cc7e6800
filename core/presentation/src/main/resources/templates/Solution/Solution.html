{#include page}
  {#title}{m:allocation_page_heading}{/title}
  {#heading}{m:allocation_page_heading}{/heading}

  <div id="actual-solution">
    <div x-data="tripDataAlpine()" x-init="init()">
      {#include Solution/Details /}
      <div>
        {#each solution.vehicles}
          <a class="btn btn-sm btn-outline-secondary" @click="onVehicleChange({it.id})">{it.name}</a>
        {/each}
      </div>
    </div>
    <br>
  </div>
{/include}
