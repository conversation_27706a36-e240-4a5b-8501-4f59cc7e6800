<div class="card" id="run-details"
  {#if solverInProgress }
    up-poll up-interval="2000" up-source="{uri:Solution.runDetails(date)}"
  {/if}
>
  <div class="card-header">
    <div class="card-title">{#if solverInProgress }Solving{#else}New Solution{/if}</div>
  </div>
  <div class="card-body">
    {#if solverInProgress }
      <h3 class="text-success">In Progress</h3>
    {#else}
    <form action="{uri:Solution.solve()}" method="post" up-target="#run-details">
      <div class="col-md-6 mb-3">
        <label for="solve_date" class="form-label">Date</label>
        <input id="solve_date" class="form-control" type="date" name="date" value="{date}">
      </div>
      <button type="submit" class="btn btn-success">{m:allocate}</button>
    </form>
    {/if}
  </div>
</div>
{#messages /}
