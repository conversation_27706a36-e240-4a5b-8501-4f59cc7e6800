<div id="solution">
    <div class="row" id="vehicles_tables">
      <div class="vehicle_table col-6">
        <h3>Google</h3>
        <table class="table table-sm">
          <thead>
          <tr>
            <th>Job Id</th>
            <th>Vehicle</th>
            <th>Total Time: {solution.readableTotalTime}</th>
          </tr>
          </thead>
          <tbody>
          {#for s in solution.vehicleRoutes}
            <tr class="v_{s.vehicle.id}" x-show="selectedVehicle == {s.vehicle.id}" {!style="color: {s.vehicle.color}"!}>
              <th>{s.vehicle.name}</th>
              <th>{s.vehicle.canHandle}</th>
              <th>{s.vehicle.time.readableTotalTime}</th>
            </tr>
            {#each s.visits}
              <tr class="v_{s.vehicle.id}" x-show="selectedVehicle == {s.vehicle.id}" {!style="color: {s.vehicle.color}"!}>
              <td>{it.jobId}</td>
              <td>{it.name} {it.type}</td>
              <td>{it.arrivalTime} to {it.departureTime}</td>
              </tr>
            {/each}
          {/for}
          </tbody>
        </table>
      </div>
      <div id="naviTimeResult" class="col-6">
        <h3>Navitime</h3>
        <table class="table table-sm">
          <thead>
          <tr>
            <th colspan="3">&nbsp;</th>
          </tr>
          </thead>
          <tbody>
          <tr>
            <th colspan="3" x-text="totalTime"></th>
          </tr>
          <template x-for="visit in visits" :key="visit.id">
            <tr>
              <td></td>
              <td x-text="visit.name"></td>
              <td x-text="visit.arrivalTime + ' to ' + (visit.departureTime || '00:00')"></td>
            </tr>
          </template>
          </tbody>
        </table>
      </div>
    </div>
    <i id="data" up-data="{data}"></i>
</div>
