plugins {
  id("org.kordamp.gradle.jandex") version "1.1.0"
}

val renardeVersion: String by project
val csvLibVersion: String by project
dependencies {
  implementation(project(":core:domain"))
  implementation(project(":core:application"))

  implementation("io.quarkiverse.renarde:quarkus-renarde:${renardeVersion}")
  implementation("io.quarkus:quarkus-rest-jackson")
  implementation("io.quarkiverse.web-bundler:quarkus-web-bundler:1.7.1")
  implementation("com.fasterxml.jackson.dataformat:jackson-dataformat-csv:${csvLibVersion}")

  implementation("org.mvnpm:bootstrap:5.3.2")
  implementation("org.mvnpm:bootstrap-icons:1.11.0")
  implementation("org.mvnpm:unpoly:3.9.0")
  implementation("org.mvnpm:jquery:3.7.1")
  implementation("org.mvnpm:alpinejs:3.14.1")
  implementation("org.mvnpm.at.alpinejs:persist:3.14.1")
  implementation("org.mvnpm.at.popperjs:core:2.11.8")
  implementation("org.mvnpm:sweetalert2:11.13.1")
  implementation("org.mvnpm:tom-select:2.3.1")
  implementation("org.mvnpm:adminlte4:4.0.0-beta.1.20240530")
}
