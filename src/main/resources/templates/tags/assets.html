{#let key?="main" tag?="both"}
{#if tag is "script" or tag is "both"}
  {#let src=inject:bundle.script(key)}
  {#if src}
    <script type="module" src="{src}" defer></script>
  {#else}
    <!-- no script found for key '{key}' in Bundler mapping !-->
  {/if}
{/if}
{#if key != "chunk"}
  {#if tag is "style" or tag is "both"}
    {#let href=inject:bundle.style(key)}
    {#if href}
      <link rel="stylesheet" href="{href}" />
    {#else}
      <!-- no style found for key '{key}' in Bundler mapping !-->
    {/if}
  {/if}
{/if}
