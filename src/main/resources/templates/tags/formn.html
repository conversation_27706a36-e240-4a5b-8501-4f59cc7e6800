<form action="/{it}/{#if isEdit}update/{id??}{#else}create{/if}" method="{method ?: 'post'}"
      class="row g-3 needs-validation{#if errors != null} was-validated{/if}"
      autocomplete="off" {#if up-layer??} up-layer="{up-layer}"{/if}>
    {#authenticityToken/}
  {nested-content}
  <div class="d-grid gap-2 d-md-flex justify-content-md-end">
    <button type="reset" class="btn btn-secondary">{m:reset}</button>
    <button type="submit" class="btn btn-primary">{m:submit}</button>
  </div>
</form>
