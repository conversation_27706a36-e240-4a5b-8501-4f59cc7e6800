{#if page.totalPages > 1}
  <nav aria-label="Page navigation">
  <ul class="pagination">
  <li class="page-item{#if page.page == 1} disabled{/if}">
    <a class="page-link"
       href="{uri}?size={page.size}&page={page.previousPage}&sort={page.sort}&order={page.order}"
       aria-label="Previous" title="Previous Page" rel="tooltip">
      <span aria-hidden="true">&laquo;</span>
    </a>
  </li>
  {#for i in page.numbers}
    <li class="page-item{#if page.page == i} active{/if}">
    <a class="page-link" href="{uri}?size={page.size}&page={i}&sort={page.sort}&order={page.order}" title="Page {i}" rel="tooltip">{i}</a>
    </li>
  {/for}
  {#if page.hasMore}
    <li class="page-item disabled">
      <a class="page-link" href="#">
        <span data-feather="more-horizontal" width="20" height="20"></span>...
      </a>
    </li>
  {/if}
  <li class="page-item{#if page.page == page.totalPages} disabled{/if}">
  <a class="page-link" href="{uri}?size={page.size}&page={page.nextPage}&sort={page.sort}&order={page.order}"
  aria-label="Next" title="Next Page" rel="tooltip">
    <span aria-hidden="true">&raquo;</span>
  </a>
  </li>
  </ul>
  </nav>
{/if}
