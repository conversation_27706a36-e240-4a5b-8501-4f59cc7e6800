{@java.lang.Long solutionId}
{@com.sd.loms.presentation.dto.SolutionData solution}

<div class="mt-4">
    <div class="card" id="solution-details">
        <div class="card-header {#if solution.manuallyChanged}text-bg-warning{/if}">
            <div class="card-title">
                結果の詳細
                {#if solution.manuallyChanged}
                    「手動で変更されました」
                {/if}
            </div>
        </div>
        <div class="card-body">
            <div x-data="tripDataAlpine()" x-init="init()">
                <div class="mb-3">
                    <button class="btn btn-outline-primary" @click="showCompleteResult = !showCompleteResult">完全な結果を切り替え</button>
                    {#if solution.manuallyChanged}
                        <a class="btn btn-danger"
                           up-confirm="本当に削除しますか?"
                           up-method="delete"
                           href="/solution/delete/{solution.id}">
                            <i class="bi-trash"></i>
                            削除
                        </a>
                    {/if}
                </div>
                <div class="mb-3">
                    <h3>車両</h3>
                    {#for vehicle in solution.vehicleRoutes}
                        <a class="btn btn-sm btn-outline-secondary"
                           @click="onVehicleChange(\{ id: {vehicle.vehicle.id}, name: '{vehicle.vehicle.name}' \})">
                            {vehicle.vehicle.id} - {vehicle.vehicle.name}
                        </a>
                    {/for}
                </div>
            </div>
        </div>
    </div>
</div>
