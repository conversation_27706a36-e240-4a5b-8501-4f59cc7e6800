<div up-main data-csrf-token="{inject:csrf.token}">
  <h3 class="mb-0">{title}</h3>
  
  <form method="post" action="{uri:Vehicles.save()}" class="row g-3" autocomplete="off">
    {#authenticityToken/}
    {#messages/}
    
    {#if vehicle.id}
      <input type="hidden" name="id" value="{vehicle.id}"/>
    {/if}
    
    <div class="col-md-6">
      <label for="groupId" class="form-label">グループ *</label>
      <select id="groupId" name="groupId" class="form-select {#if validationFailed('groupId')}is-invalid{/if}">
        <option value="">選択してください</option>
        {#for group in groups}
          <option value="{group.id}" {#if group.id == vehicle.groupId}selected{/if}>
            {group.nm}
          </option>
        {/for}
      </select>
      {#if validationFailed('groupId')}
        <div class="invalid-feedback">{validationMessage('groupId')}</div>
      {/if}
    </div>
    
    <div class="col-md-6">
      <label for="name" class="form-label">車両名 *</label>
      <input id="name" name="name" type="text" class="form-control {#if validationFailed('name')}is-invalid{/if}" value="{vehicle.name}" autofocus/>
      {#if validationFailed('name')}
        <div class="invalid-feedback">{validationMessage('name')}</div>
      {/if}
    </div>
    
    <div class="col-md-6">
      <label for="serialNm" class="form-label">シリアル番号</label>
      <input id="serialNm" name="serialNm" type="text" class="form-control" value="{vehicle.serialNm}"/>
    </div>
    
    <div class="col-md-6">
      <label for="number" class="form-label">車両番号</label>
      <input id="number" name="number" type="text" class="form-control" value="{vehicle.number}"/>
    </div>
    
    <div class="col-md-6">
      <label for="numberPlate" class="form-label">ナンバープレート</label>
      <input id="numberPlate" name="numberPlate" type="text" class="form-control" value="{vehicle.numberPlate}"/>
    </div>
    
    <div class="col-md-6">
      <label for="model" class="form-label">車種</label>
      <input id="model" name="model" type="text" class="form-control" value="{vehicle.model}"/>
    </div>
    
    <div class="col-md-4">
      <label for="maximumLoadCapacity" class="form-label">最大積載量 (kg)</label>
      <input id="maximumLoadCapacity" name="maximumLoadCapacity" type="number" class="form-control" value="{vehicle.maximumLoadCapacity}"/>
    </div>
    
    <div class="col-md-4">
      <label for="frontTankCapacity" class="form-label">前タンク容量 (L)</label>
      <input id="frontTankCapacity" name="frontTankCapacity" type="number" class="form-control" value="{vehicle.frontTankCapacity}"/>
    </div>
    
    <div class="col-md-4">
      <label for="rearTankCapacity" class="form-label">後タンク容量 (L)</label>
      <input id="rearTankCapacity" name="rearTankCapacity" type="number" class="form-control" value="{vehicle.rearTankCapacity}"/>
    </div>
    
    <div class="col-md-4">
      <label for="totalTankCapacity" class="form-label">総タンク容量 (L)</label>
      <input id="totalTankCapacity" name="totalTankCapacity" type="number" class="form-control" value="{vehicle.totalTankCapacity}"/>
    </div>
    
    <div class="col-md-4">
      <label for="displayOrder" class="form-label">表示順</label>
      <input id="displayOrder" name="displayOrder" type="number" class="form-control" value="{vehicle.displayOrder}"/>
    </div>
    
    <div class="col-md-4">
      <div class="form-check mt-4">
        <input class="form-check-input" type="checkbox" id="belongFlg" name="belongFlg" {#if vehicle.belongFlg}checked{/if}/>
        <label class="form-check-label" for="belongFlg">
          所属フラグ
        </label>
      </div>
    </div>
    
    <div class="col-md-12">
      <label for="remarks" class="form-label">備考</label>
      <textarea id="remarks" name="remarks" class="form-control" rows="3">{vehicle.remarks}</textarea>
    </div>
    
    <div class="col-md-12">
      <label class="form-label">対応可能材料</label>
      <div class="row">
        {#for item in items}
          <div class="col-md-3">
            <div class="form-check">
              <input class="form-check-input" type="checkbox" name="materialCategoryIds" value="{item.id}" 
                     id="item_{item.id}" {#if vehicle.materialCategoryIds.contains(item.id)}checked{/if}/>
              <label class="form-check-label" for="item_{item.id}">
                {item.name}
              </label>
            </div>
          </div>
        {/for}
      </div>
    </div>
    
    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
      <button type="reset" class="btn btn-secondary">リセット</button>
      <button type="submit" class="btn btn-primary">保存</button>
    </div>
  </form>
</div>
