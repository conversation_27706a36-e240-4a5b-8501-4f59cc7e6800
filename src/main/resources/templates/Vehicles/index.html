{#include page}
  {#title}{m:vehicle}{/title}
  {#heading}{m:vehicle}{/heading}

  <div class="row mb-3">
    <div class="col-12">
      <a href="{uri:Vehicles.addNew()}" class="btn btn-outline-primary mb-3 me-3" up-layer="new" up-history="false" up-on-dismissed="up.reload('.table')">
        <i class="bi-plus-lg"></i> 新規登録
      </a>
    </div>
  </div>

  <div class="row">
    <div class="col-12">
      <div class="card">
        <div class="card-header">
          <div class="card-title">車両一覧</div>
        </div>
        <div class="card-body">
          <table class="table table-striped table-hover table-bordered">
            <thead class="table-success">
              <tr>
                <th>ID</th>
                <th>車両名</th>
                <th>車両番号</th>
                <th>グループ</th>
                <th>対応可能</th>
                <th>最大積載量</th>
                <th>タンク容量</th>
                <th>操作</th>
              </tr>
            </thead>
            <tbody>
              {#for vehicle in vehicles}
                <tr>
                  <td>
                    <a href="{uri:Vehicles.details(vehicle.id)}" up-history="false" up-layer="new" up-on-dismissed="up.reload('.table')">
                      {vehicle.id}
                    </a>
                  </td>
                  <td>
                    <a href="{uri:Vehicles.details(vehicle.id)}" up-history="false" up-layer="new" up-on-dismissed="up.reload('.table')">
                      {vehicle.name}
                    </a>
                  </td>
                  <td>{vehicle.number}</td>
                  <td>{vehicle.groupId}</td>
                  <td>{vehicle.canHandleNames}</td>
                  <td>{vehicle.maximumLoadCapacity} kg</td>
                  <td>{vehicle.totalTankCapacity} L</td>
                  <td>
                    <a up-method="delete" href="{uri:Vehicles.delete(vehicle.id)}" up-confirm="削除してもよろしいですか？" up-history="false" class="btn btn-sm btn-outline-danger">
                      削除
                    </a>
                  </td>
                </tr>
              {/for}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>
{/include}
