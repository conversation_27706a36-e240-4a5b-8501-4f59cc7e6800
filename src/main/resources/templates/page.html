{#if !flash:refresh && inject:hxConfig.version != null}
<html lang="jp">
<head>
	<title>{m:app_title} | {#insert title}{/}</title>
</head>
{#include page$main_main /}
</html>
{#else}
{#include layout}
{#fragment id=main_main}
{#if inject:hxConfig.mode != null && inject:hxConfig.mode == 'modal'}
<div up-flashes>
	{#if flash:message}<div class="toast" role="alert" data-type="success" data-text="{flash:message}"></div>{/if}
	{#if flash:refresh}<div class="toast" role="alert" data-type="success" data-text="{flash:refresh}"></div>{/if}
</div>
<div up-main>
	<h3 class="mb-0">{#insert heading}{/}</h3>
	{#insert}No body!{/}
</div>
{#else}
<main id="main" class="app-main" up-main="root">
	<div up-flashes>
		{#if flash:message}<div class="toast" role="alert" data-type="success" data-text="{flash:message}"></div>{/if}
		{#if flash:refresh}<div class="toast" role="alert" data-type="success" data-text="{flash:refresh}"></div>{/if}
	</div>
	<div class="app-content-header">
		<div class="container-fluid">
			<div class="row">
				<div class="col-sm-6">
					<h3 class="mb-0">{#insert heading}{/}</h3>
				</div>
				<div class="col-sm-6">
					{!<ol class="breadcrumb float-sm-end">
					  <li class="breadcrumb-item active">Home</li>
					</ol>!}
				</div>
			</div>
		</div>
	</div>
	<div class="app-content">
		<div class="container-fluid">
			{#insert}No body!{/}
		</div>
	</div>
</main>
{/if}
{/fragment}
{/include}
{/if}


