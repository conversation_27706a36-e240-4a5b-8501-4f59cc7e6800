{#include page}
  {#title}{m:driver}{/title}
  {#heading}{m:driver}{/heading}

  <div class="row mb-3">
    <div class="col-12">
      <a href="{uri:Drivers.addNew()}" class="btn btn-outline-primary mb-3 me-3" up-layer="new" up-history="false" up-on-dismissed="up.reload('.table')">
        <i class="bi-plus-lg"></i> 新規登録
      </a>
    </div>
  </div>

  <div class="row">
    <div class="col-12">
      <div class="card">
        <div class="card-header">
          <div class="card-title">乗務員一覧</div>
        </div>
        <div class="card-body">
          <table class="table table-striped table-hover table-bordered">
            <thead class="table-success">
              <tr>
                <th>ID</th>
                <th>氏名</th>
                <th>従業員コード</th>
                <th>グループ</th>
                <th>対応可能</th>
                <th>主車両</th>
                <th>携帯</th>
                <th>操作</th>
              </tr>
            </thead>
            <tbody>
              {#for driver in drivers}
                <tr>
                  <td>
                    <a href="{uri:Drivers.details(driver.id)}" up-history="false" up-layer="new" up-on-dismissed="up.reload('.table')">
                      {driver.id}
                    </a>
                  </td>
                  <td>
                    <a href="{uri:Drivers.details(driver.id)}" up-history="false" up-layer="new" up-on-dismissed="up.reload('.table')">
                      {driver.name}
                    </a>
                  </td>
                  <td>{driver.employeeCd}</td>
                  <td>{driver.groupId}</td>
                  <td>{driver.canHandleNames}</td>
                  <td>{driver.vehicleId}</td>
                  <td>{driver.mobile}</td>
                  <td>
                    <a up-method="delete" href="{uri:Drivers.delete(driver.id)}" up-confirm="削除してもよろしいですか？" up-history="false" class="btn btn-sm btn-outline-danger">
                      削除
                    </a>
                  </td>
                </tr>
              {/for}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>
{/include}
