<div up-main data-csrf-token="{inject:csrf.token}">
  <h3 class="mb-0">{title}</h3>
  
  <form method="post" action="{uri:Drivers.save()}" class="row g-3" autocomplete="off">
    {#authenticityToken/}
    {#messages/}
    
    {#if driver.id}
      <input type="hidden" name="id" value="{driver.id}"/>
    {/if}
    
    <div class="col-md-6">
      <label for="groupId" class="form-label">グループ *</label>
      <select id="groupId" name="groupId" class="form-select {#if validationFailed('groupId')}is-invalid{/if}">
        <option value="">選択してください</option>
        {#for group in groups}
          <option value="{group.id}" {#if group.id == driver.groupId}selected{/if}>
            {group.nm}
          </option>
        {/for}
      </select>
      {#if validationFailed('groupId')}
        <div class="invalid-feedback">{validationMessage('groupId')}</div>
      {/if}
    </div>
    
    <div class="col-md-6">
      <label for="locationId" class="form-label">所在地 *</label>
      <select id="locationId" name="locationId" class="form-select {#if validationFailed('locationId')}is-invalid{/if}">
        <option value="">選択してください</option>
        {#for location in locations}
          <option value="{location.id}" {#if location.id == driver.locationId}selected{/if}>
            {location.add1} {location.add2}
          </option>
        {/for}
      </select>
      {#if validationFailed('locationId')}
        <div class="invalid-feedback">{validationMessage('locationId')}</div>
      {/if}
    </div>
    
    <div class="col-md-6">
      <label for="nm1" class="form-label">氏名 *</label>
      <input id="nm1" name="nm1" type="text" class="form-control {#if validationFailed('nm1')}is-invalid{/if}" value="{driver.nm1}" autofocus/>
      {#if validationFailed('nm1')}
        <div class="invalid-feedback">{validationMessage('nm1')}</div>
      {/if}
    </div>
    
    <div class="col-md-6">
      <label for="employeeCd" class="form-label">従業員コード</label>
      <input id="employeeCd" name="employeeCd" type="text" class="form-control" value="{driver.employeeCd}"/>
    </div>
    
    <div class="col-md-6">
      <label for="nm2" class="form-label">氏名2</label>
      <input id="nm2" name="nm2" type="text" class="form-control" value="{driver.nm2}"/>
    </div>
    
    <div class="col-md-6">
      <label for="kana2" class="form-label">カナ</label>
      <input id="kana2" name="kana2" type="text" class="form-control" value="{driver.kana2}"/>
    </div>
    
    <div class="col-md-6">
      <label for="snm" class="form-label">略称</label>
      <input id="snm" name="snm" type="text" class="form-control" value="{driver.snm}"/>
    </div>
    
    <div class="col-md-6">
      <label for="mobile" class="form-label">携帯電話</label>
      <input id="mobile" name="mobile" type="tel" class="form-control" value="{driver.mobile}"/>
    </div>
    
    <div class="col-md-6">
      <label for="vehicleId" class="form-label">主車両</label>
      <select id="vehicleId" name="vehicleId" class="form-select">
        <option value="">選択してください</option>
        {#for vehicle in vehicles}
          <option value="{vehicle.id}" {#if vehicle.id == driver.vehicleId}selected{/if}>
            {vehicle.name}
          </option>
        {/for}
      </select>
    </div>
    
    <div class="col-md-6">
      <label for="salaryId" class="form-label">給与</label>
      <select id="salaryId" name="salaryId" class="form-select">
        <option value="">選択してください</option>
        {#for salary in salaries}
          <option value="{salary.id}" {#if salary.id == driver.salaryId}selected{/if}>
            クラス: {salary.clazz}, ランク: {salary.rank}
          </option>
        {/for}
      </select>
    </div>
    
    <div class="col-md-6">
      <label for="keisu" class="form-label">係数</label>
      <input id="keisu" name="keisu" type="number" step="0.01" class="form-control" value="{driver.keisu}"/>
    </div>
    
    <div class="col-md-6">
      <label for="jobType" class="form-label">職種</label>
      <input id="jobType" name="jobType" type="number" class="form-control" value="{driver.jobType}"/>
    </div>
    
    <div class="col-md-6">
      <label for="employedDate" class="form-label">入社日</label>
      <input id="employedDate" name="employedDate" type="date" class="form-control" value="{driver.employedDate}"/>
    </div>
    
    <div class="col-md-6">
      <label for="quitDate" class="form-label">退職日</label>
      <input id="quitDate" name="quitDate" type="date" class="form-control" value="{driver.quitDate}"/>
    </div>
    
    <div class="col-md-6">
      <div class="form-check mt-4">
        <input class="form-check-input" type="checkbox" id="belongFlg" name="belongFlg" {#if driver.belongFlg}checked{/if}/>
        <label class="form-check-label" for="belongFlg">
          所属フラグ
        </label>
      </div>
    </div>
    
    <div class="col-md-6">
      <div class="form-check mt-4">
        <input class="form-check-input" type="checkbox" id="quitFlg" name="quitFlg" {#if driver.quitFlg}checked{/if}/>
        <label class="form-check-label" for="quitFlg">
          退職フラグ
        </label>
      </div>
    </div>
    
    <div class="col-md-12">
      <label for="cmtJob" class="form-label">職務コメント</label>
      <textarea id="cmtJob" name="cmtJob" class="form-control" rows="2">{driver.cmtJob}</textarea>
    </div>
    
    <div class="col-md-12">
      <label for="remarks" class="form-label">備考</label>
      <textarea id="remarks" name="remarks" class="form-control" rows="3">{driver.remarks}</textarea>
    </div>
    
    <div class="col-md-12">
      <label class="form-label">対応可能材料</label>
      <div class="row">
        {#for item in items}
          <div class="col-md-3">
            <div class="form-check">
              <input class="form-check-input" type="checkbox" name="materialCategoryIds" value="{item.id}" 
                     id="item_{item.id}" {#if driver.materialCategoryIds.contains(item.id)}checked{/if}/>
              <label class="form-check-label" for="item_{item.id}">
                {item.name}
              </label>
            </div>
          </div>
        {/for}
      </div>
    </div>
    
    <div class="col-md-12">
      <label class="form-label">運転可能車両</label>
      <div class="row">
        {#for vehicle in vehicles}
          <div class="col-md-3">
            <div class="form-check">
              <input class="form-check-input" type="checkbox" name="vehicleIds" value="{vehicle.id}" 
                     id="vehicle_{vehicle.id}" {#if driver.vehicleIds.contains(vehicle.id)}checked{/if}/>
              <label class="form-check-label" for="vehicle_{vehicle.id}">
                {vehicle.name}
              </label>
            </div>
          </div>
        {/for}
      </div>
    </div>
    
    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
      <button type="reset" class="btn btn-secondary">リセット</button>
      <button type="submit" class="btn btn-primary">保存</button>
    </div>
  </form>
</div>
