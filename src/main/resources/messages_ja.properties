app_title=LOMS
allocation_page_heading=è»ä¸¡ããã©ã¤ãã¼ãéè»
home_page_heading=ãã ãã¼ã¸
allocate=éè»
location=ææ± ã»å±ãå
score=ã¹ã³ã¢
status=ã¹ãã¼ã¿ã¹
not_started=éå§ããã¦ãã¾ãã
solving=éè»ä¸­
completed=éè»å®äº
number=çªå·
execution_date=å®è¡æ¥ä»
loading_point=ææ± 
delivery_point=å±ãå
material_category=ææã«ãã´ãª
weight=éã
liters=ãªããã«
driver=ä¹åå¡
vehicle=è»ä¸¡
color=è²
arrival_time=å°çæå»
service_duration=ãµã¼ãã¹æé
departure_time=åºçºæé
add=æ°è¦ç»é²
details=è©³ç´°
masters=ãã¹ã¿ã¼
load=åå¾
submit=æ°è¦ç»é²
reset=ãªã»ãã
create=%s æ°è¦ç»é²
en=English
ja=æ¥æ¬èª
language_changed=è¨èªã%sã«å¤æ´ãã¾ããã
created_message=%s ç»é²ãã¾ããã
updated_message=%s æ´æ°ãã¾ããã
can_handle=å¯¾å¿å¯è½
id=ID


add1=Address 1
add2=Address 2
add3=Address 3
zip=Zip

csv_import=CSV ã¤ã³ãã¼ã
csv_file=CSV ãã¡ã¤ã«
import_in_progress=è·é¢ãããªãã¯ã¹ã®æ´æ°ãã­ã»ã¹ãé²è¡ä¸­ã§ãã
duplicated_in_rows=è¡ã«éè¤ãã¦ãã¾ã: %s
location_exists_ib_db=%sã %s ã¾ãã¯åå ã%sãã ãã§ã«ç»é²æ¸ã¿ã§ãã
csv_import_succeeded_distance_matrix_update_in_progress=ææ± ã»å±ãåã®CSV ãã¤ã³ãã¼ãããã¾ãããè·é¢ãããªãã¯ã¹ã®æ´æ°ãã­ã»ã¹ãé²è¡ä¸­ã§ãã

login=ã­ã°ã¤ã³
email=ã¡ã¼ã«ã¢ãã¬ã¹
password=ãã¹ã¯ã¼ã

allocate_now=ã¢ããã­ã¼ãããã¾ããã
load_data=ãã¼ã¿ã¼ãè·åãã
