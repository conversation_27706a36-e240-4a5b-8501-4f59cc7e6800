import 'admin-lte'
import 'unpoly'
import Alpine from 'alpinejs'
import persist from '@alpinejs/persist'
import $ from 'jquery';
import 'bootstrap';
import "admin-lte";
import Swal from 'sweetalert2';
import TomSelect from 'tom-select';

let alpine = Alpine
alpine.plugin(persist)
alpine.start()

window.Alpine = alpine



up.protocol.config.csrfToken = function (){
	let element = document.querySelector('[data-csrf-token]')
	if(!element) return "";
	return element.dataset.csrfToken
}
up.fragment.config.mainTargets.push('main')
up.feedback.config.currentClasses = ['active']
up.link.config.followSelectors.push('a[href]');
up.form.config.submitSelectors.push(['form']);
up.fragment.config.navigateOptions.transition = 'cross-fade';
up.fragment.config.navigateOptions.cache = false;
if (window.location.hostname.toLowerCase() === 'localhost') {
	up.log.enable();
} else {
	up.log.disable();
}
window.navitimeConfig = { apiUrl: '', customerId: '', signature: '', requestCode: '', toBeLoadedFrom: '' };
let mapSelector = '#map';
up.compiler(mapSelector, function (element, data) {
	initMap(data)
});


let Toast = Swal.mixin({
	toast: true,
	position: "top-end",
	showConfirmButton: false,
	timer: 3e3,
	width: "auto",
	didOpen: (toast) => {
		const progress = document.createElement('div');
		Object.assign(progress.style, {
			position: 'absolute',
			bottom: '0',
			left: '0',
			width: '100%',
			height: '3px'
		});
		toast.appendChild(progress);

		let width = 100;
		const interval = setInterval(() => {
			width -= 0.5;
			progress.style.width = width + '%';
			if (width <= 0) {
				clearInterval(interval);
			}
		}, 15);

		// Set progress bar color based on the type of alert
		const alertType = Array.from(toast.classList)
			.find(cls => cls.startsWith('swal2-icon'))
			.replace('swal2-icon-', '');

		switch (alertType) {
			case 'success':
				progress.classList.add('bg-success');
				toast.classList.add('text-success');
				break;
			case 'info':
				progress.classList.add('bg-info');
				toast.classList.add('text-info');
				break;
			case 'warning':
				progress.classList.add('bg-warning');
				toast.classList.add('text-warning');
				break;
			case 'error':
				progress.classList.add('bg-danger');
				toast.classList.add('text-danger');
				break;
			default:
				progress.classList.add('bg-info');
				toast.classList.add('text-info');
		}
	}
});

up.compiler('.toast', function (element, data) {
	Toast.fire({
		icon: data.type,
		title: data.text
	})
});

up.compiler('.tom-select', function (element) {
	let plugins = ['dropdown_input'];
	if (element.hasAttribute('multiple')) {
		plugins.push('remove_button');
	}
	new TomSelect(element, {plugins});
})

up.on('app:map-loaded', function (event) {
	$('#map-loading').slideUp();
});

up.on('up:fragment:inserted', (event, fragment) => {
	fragment.classList.add('new-fragment', 'inserted')
	up.util.timer(0, () => fragment.classList.remove('inserted'))
	up.util.timer(1000, () => fragment.classList.remove('new-fragment'))
});

window.reload = () => {
	// TODO commented the SPA like reload due to css issues, need to fix
	// up.reload({ target: 'body', url: window.location.href })
	window.location.reload();
}
