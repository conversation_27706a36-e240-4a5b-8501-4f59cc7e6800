package com.sd.loms.domain

import io.quarkus.runtime.annotations.RegisterForReflection

@RegisterForReflection
@JvmRecord
data class VehicleRouteVM(val vehicle: VehicleVM, val visits: List<VisitVM>)


@RegisterForReflection
@JvmRecord
data class VehicleVM(
	val id: Long,
	val driverId: Long,
	val canHandle: Set<String>,
	val time: Long
)


@RegisterForReflection
@JvmRecord
data class VisitVM(
	val id: Long,
	val uniqueId: String,
	val jobId: Long,
	val type: VisitType,
	val visitIndex: Int,
	val item: String,
	val arrivalTime: Long,
	val serviceDuration: Long,
	val departureTime: Long,
	val distanceToDepot: Long? = null,
	val distanceFromPreviousVisit: Long? = null
)
