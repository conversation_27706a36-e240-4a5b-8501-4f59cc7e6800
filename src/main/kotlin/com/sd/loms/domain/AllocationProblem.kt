package com.sd.loms.domain

import java.time.LocalDate


data class AllocationProblem(
	val withHistory: Boolean,
	val withEvenAllocation: Boolean,
	val locations: List<Location>,
	val vehicles: List<VehicleDto>,
	val drivers: List<DriverDto>,
	val patterns: List<PatternDto>,
	val distances: List<DistanceDto>,
	val orders: List<OrderDto>,
	val items: List<Item>
)

data class PatternDto(
	val shipperId: Long,
	val pickupId: Long,
	val deliveryId: Long,
	val itemId: Long,
	val vehicleId: Long,
	val driverId: Long,
)

data class OrderDto(
	val id: Long,
	val pickupId: Long,
	val deliveryId: Long,
	val itemId: Long,
	val shipperId: Long,
	val fulfillDate: Long,
	val weight: Int,
	val litres: Int,
)

data class VehicleDto(
	val id: Long,
	val name: String,
	val color: String,
	val weightCapacity: Int,
	val totalTankCapacity: Int,
	val frontTankCapacity: Int,
	val rearTankCapacity: Int,
	val canHandle: Set<Long>
) {

	fun toDomainVehicle(items: List<Item>): Vehicle {
		val vehicle = Vehicle(
			id, name, color, weightCapacity,
			totalTankCapacity, frontTankCapacity, rearTankCapacity
		)
		vehicle.canHandle = toItems(canHandle, items)
		return vehicle
	}

}

data class DriverDto(
	val id: Long,
	val name: String,
	val primaryVehicle: Long,
	val canHandle: Set<Long>,
	val canDrive: Set<Long>,
)

data class DistanceDto(val fromLoc: Long, val toLoc: Long, val distance: Long)


fun toItems(canHandle: Set<Long>, items: List<Item>) = canHandle.mapNotNull { items.find { c -> c.id == it } }.toSet()
