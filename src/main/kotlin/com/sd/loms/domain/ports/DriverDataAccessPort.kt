package com.sd.loms.domain.ports

import com.sd.loms.domain.DriverDto
import com.sd.loms.domain.Item
import com.sd.loms.domain.Vehicle
import com.sd.loms.presentation.dto.GroupData
import com.sd.loms.presentation.dto.LocationData
import com.sd.loms.presentation.dto.SalaryData

interface DriverDataAccessPort {
    fun getAllDrivers(): List<DriverDto>
    fun getDriverById(id: Long): DriverDto?
    fun saveDriver(driver: DriverDto): Long
    fun updateDriver(driver: DriverDto): DriverDto
    fun deleteDriver(id: Long)
    fun getAllGroups(): List<GroupData>
    fun getAllItems(): List<Item>
    fun getAllVehicles(): List<Vehicle>
    fun getAllSalaries(): List<SalaryData>
    fun getAllLocations(): List<LocationData>
}
