package com.sd.loms.domain.ports

import com.sd.loms.domain.Coordinates
import com.sd.loms.domain.Distance
import com.sd.loms.domain.DistanceDto
import com.sd.loms.domain.Location


interface DistanceMatrixPort {
	fun clear()
	fun distance(from: Coordinates, to: Coordinates): Distance
	fun put(from: Coordinates, to: Coordinates, distance: Distance)
	fun removeLocation(location: Coordinates)
	fun populateDistanceMatrixFromDB(locations: List<Location>, distances: List<DistanceDto>)
}
