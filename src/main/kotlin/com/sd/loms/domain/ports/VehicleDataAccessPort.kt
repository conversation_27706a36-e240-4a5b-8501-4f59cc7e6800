package com.sd.loms.domain.ports

import com.sd.loms.domain.Vehicle
import com.sd.loms.domain.Item
import com.sd.loms.presentation.dto.GroupData

interface VehicleDataAccessPort {
    fun getAllVehicles(): List<Vehicle>
    fun getVehicleById(id: Long): Vehicle?
    fun saveVehicle(vehicle: Vehicle): Long
    fun updateVehicle(vehicle: Vehicle): Vehicle
    fun deleteVehicle(id: Long)
    fun getAllGroups(): List<GroupData>
    fun getAllItems(): List<Item>
}
