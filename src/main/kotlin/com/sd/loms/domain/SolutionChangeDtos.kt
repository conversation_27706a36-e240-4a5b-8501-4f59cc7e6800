package com.sd.loms.domain

import ai.timefold.solver.core.api.score.analysis.ScoreAnalysis
import ai.timefold.solver.core.api.score.buildin.hardmediumsoftlong.HardMediumSoftLongScore
import com.sd.loms.infrastructure.planning.adapter.model.VehicleRoutingSolution

data class SolutionChangeValidationRequest(
	val runId: Long,
	val solutionId: Long,
	val fromProblem: AllocationProblem?,
	val toProblem: AllocationProblem,
	val changes: List<IndexDetails>,
	val groupChanged: Boolean,
)

data class IndexDetails(val jobId: Long, val newIndex: Int, val oldIndex: Int)
data class SolutionChangeValidationResponse(val isValid: Boolean, val messages: List<String>, val solution: Solution?)

data class SolutionValidation(
	val isValid: Boolean,
	val messages: List<String>,
	val solution: Solution?
)

data class AnalysisResult(
	val solution: VehicleRoutingSolution,
	val score: HardMediumSoftLongScore,
	val analysis: ScoreAnalysis<HardMediumSoftLongScore>,
	val messages: List<String>,
)
