package com.sd.loms.domain


import io.quarkus.runtime.annotations.RegisterForReflection
import kotlin.math.ceil

@RegisterForReflection
data class Page<T>(val content: Iterable<T>, val total: Long, val paging: PageRequest) : Iterable<T> {
	override fun iterator() = content.iterator()
	fun totalPages() = if (paging.size == 0) 1 else ceil(total.toDouble() / paging.size.toDouble()).toInt()
	fun page() = paging.page
	fun size() = paging.size
	fun sort() = paging.sort
	fun order() = paging.order
	fun previousPage() = if (paging.page < 2) 1 else paging.page - 1
	fun nextPage() = if (paging.page < totalPages()) paging.page + 1 else totalPages()
	fun numbers() = 1..totalPages().coerceAtMost(5 + paging.page)
	fun hasMore() = paging.page + 5 < totalPages()
}


@RegisterForReflection
@JvmRecord
data class PageRequest(val sort: String, val order: String, val page: Int, val size: Int) {
	companion object {
		fun of(sort: String?, order: String?, page: Int?, size: Int?): PageRequest {
			return PageRequest(sort ?: "Id", order ?: "asc", page ?: 1, size ?: 20)
		}

		fun unPaged() = of(null, null, null, null)
	}
}
