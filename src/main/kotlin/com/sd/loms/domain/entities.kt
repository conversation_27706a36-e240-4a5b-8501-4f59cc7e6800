package com.sd.loms.domain

import java.math.BigDecimal
import java.time.LocalDate


data class AllocationRun(val id: Long, val status: RunStatus, val fulfillDate: LocalDate,
						 val withHistory: Boolean, val withEvenAllocation: Boolean)

enum class RunStatus {
	SOLVING,
	COMPLETED
}
data class Vehicle(
	val id: Long, val name: String, val color: String, val weightCapacity: Int,
	val totalTankCapacity: Int, val frontTankCapacity: Int, val rearTankCapacity: Int,
) {
	lateinit var canHandle: Set<Item>
}

data class Item(val id: Long, val name: String, val cleaningFlag: Boolean)

data class Location(
	val id: Long,
	val uniqueId: String,
	val locationType: Int,
	val description: String,
	val address: String,
	val lat: BigDecimal,
	val lon: BigDecimal,
	val depot: Boolean,
	val readyTime: Long,
	val dueTime: Long,
	val serviceDuration: Long
) {
	fun getCoordinates() = Coordinates(lat, lon)
}

data class Solution(
	val id: Long?,
	val runId: Long,
	val totalTime: Long,
	val score: String,
	val feasible: Int,
	val solutionDetails: List<SolutionDetails>,
	val manual: Boolean,
	val oldSolutionId: Long,
)

data class SolutionDetails(
	val vehicleId: Long,
	val driverId: Long,
	val orderId: Long,
	val visitIndex: Int,
	val uniqueId: String,
	val arrivalTime: Long,
	val serviceTime: Long,
	val departureTime: Long,
	val timeToDepot: Long
)

@JvmRecord
data class Coordinates(val lat: BigDecimal, val lon: BigDecimal)


data class Distance(val millis: Long) {

	init {
		require(millis >= 0) { "Milliseconds ($millis) must not be negative." }
	}

	override fun toString() =
		"%dh %dm %ds %dms".format(millis / 3600000, millis / 60000 % 60, millis / 1000 % 60, millis % 1000)

}


enum class VisitType {
	PICKUP,
	DELIVERY,
	DEPOT
}
