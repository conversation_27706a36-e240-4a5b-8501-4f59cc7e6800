package com.sd.loms.presentation.dto

// Simple data classes for templates to avoid type safety issues

data class GroupData(
    val id: Long,
    val nm: String
)

data class ItemData(
    val id: Long,
    val name: String
)

data class LocationData(
    val id: Long,
    val add1: String,
    val add2: String
)

data class SalaryData(
    val id: Long,
    val clazz: String,
    val rank: String
)

data class VehicleData(
    val id: Long,
    val name: String
)

data class RouteData(
    val vehicle: VehicleRouteData,
    val visits: List<Any>
)

data class VehicleRouteData(
    val id: Long,
    val name: String,
    val color: String
)

data class SolutionData(
    val id: Long,
    val manuallyChanged: Boolean,
    val readableTotalTime: String,
    val score: Int,
    val vehicleRoutes: List<RouteData>
)
