package com.sd.loms.presentation

import com.sd.loms.application.SolutionUseCase
import com.sd.loms.domain.AllocationProblem
import com.sd.loms.domain.SolutionChangeValidationRequest
import jakarta.ws.rs.POST
import jakarta.ws.rs.Path

@Path("/api/v1/solutions")
class SolutionController(private val solutionUseCase: SolutionUseCase) {

	@POST
	@Path("/solve")
	fun solve(problem: AllocationProblem) = solutionUseCase.solveAndListen(problem)

	@POST
	@Path("/validate-solution-change")
	fun validateSolutionChange(validationRequest: SolutionChangeValidationRequest) = solutionUseCase.validateSolutionChange(validationRequest)

}
