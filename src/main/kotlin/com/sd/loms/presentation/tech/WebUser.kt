package com.sd.loms.presentation.tech

import com.sd.loms.domain.auth.DomainUser
import com.sd.loms.domain.auth.UserStatus
import io.quarkiverse.renarde.security.RenardeUser

data class WebUser(
	val email: String,
	val password: String,
	val userName: String,
	val isAdmin: <PERSON><PERSON><PERSON>,
	val status: UserStatus
) : RenardeUser {

	companion object {
		fun fromDomainUser(domainUser: DomainUser) =
			WebUser(domainUser.email, domainUser.password, domainUser.userName, domainUser.isAdmin, domainUser.status)
	}


	override fun userId() = userName
	override fun roles(): Set<String> = if (isAdmin) setOf("admin") else setOf()
	override fun registered() = status == UserStatus.REGISTERED


}
