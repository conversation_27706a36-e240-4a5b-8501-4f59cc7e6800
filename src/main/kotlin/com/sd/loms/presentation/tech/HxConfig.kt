package com.sd.loms.presentation.tech

import io.vertx.core.http.HttpServerRequest
import jakarta.enterprise.context.RequestScoped
import jakarta.inject.Named


@RequestScoped
@Named
class HxConfig(request: HttpServerRequest) {
	val context: String?
	val failContext: String?
	val failMode: String?
	val failTarget: String?
	val mode: String?
	val target: String?
	val version: String?

	init {
		version = request.getHeader("X-Up-Version")
		context = request.getHeader("X-Up-Context")
		failContext = request.getHeader("X-Up-Fail-Context")
		failMode = request.getHeader("X-Up-Fail-Mode")
		failTarget = request.getHeader("X-Up-Fail-Target")
		mode = request.getHeader("X-Up-Mode")
		target = request.getHeader("X-Up-Target")
	}

}
