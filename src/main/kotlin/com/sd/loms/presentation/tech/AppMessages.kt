package com.sd.loms.presentation.tech

import io.quarkus.qute.i18n.Message
import io.quarkus.qute.i18n.MessageBundle

//@formatter:off
@MessageBundle
interface AppMessages {
	@Message("") fun app_title(): String
	@Message("") fun allocation_page_heading(): String
	@Message("") fun allocate(): String
	@Message("") fun menu_locations(): String
	@Message("") fun score(): String
	@Message("") fun status(): String
	@Message("") fun solving(): String
	@Message("") fun completed(): String
	@Message("") fun not_started(): String
	@Message("") fun number(): String
	@Message("") fun execution_date(): String
	@Message("") fun loading_point(): String
	@Message("") fun delivery_point(): String
	@Message("") fun material_category(): String
	@Message("") fun weight(): String
	@Message("") fun liters(): String
	@Message("") fun driver(): String
	@Message("") fun vehicle(): String
	@Message("") fun color(): String
	@Message("") fun arrival_time(): String
	@Message("") fun service_duration(): String
	@Message("") fun departure_time(): String
	@Message("") fun add(): String
	@Message("") fun details(): String
	@Message("") fun masters(): String
	@Message("") fun load(): String
}
//@formatter:on
