package com.sd.loms.presentation.tech

import com.sd.loms.application.AuthUseCases
import io.quarkiverse.renarde.security.RenardeUserProvider
import jakarta.enterprise.context.ApplicationScoped

@ApplicationScoped
class MySecuritySetup(private val authUseCases: AuthUseCases) : RenardeUserProvider {


	override fun findUser(tenantId: String, authId: String): WebUser? {
		val user = authUseCases.findByUserName(authId)
		return user?.let { WebUser.fromDomainUser(it) }
	}

}
