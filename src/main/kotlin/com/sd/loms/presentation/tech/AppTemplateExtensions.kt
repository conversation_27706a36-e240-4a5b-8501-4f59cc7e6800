package com.sd.loms.presentation.tech


import com.sd.loms.domain.Page
import com.sd.loms.domain.Solution
import com.sd.loms.domain.SolutionVM
import com.sd.loms.presentation.vm.LocationVM
import io.quarkiverse.renarde.util.I18N
import io.quarkus.qute.TemplateExtension
import java.util.concurrent.TimeUnit

@TemplateExtension
object AppTemplateExtensions {
	@JvmStatic
	fun isDelivery(location: LocationVM) = location.type == 1

	@JvmStatic
	fun isPickup(location: LocationVM) = location.type == 0

	@JvmStatic
	fun locType(location: LocationVM) = if (location.isDepot) "-"
	else if (location.type == 0) "積み地" else "届け先"

	@JvmStatic
	fun nextOrder(page: Page<*>, currentColumn: String) =
		if (page.sort().isEmpty() || !currentColumn.equals(page.sort(), ignoreCase = true)) {
			"asc"
		} else if ("desc".equals(page.order(), ignoreCase = true)) {
			"asc"
		} else {
			"desc"
		}

	@JvmStatic
	fun getCssClass(page: Page<*>, it: String) =
		if (page.sort().isEmpty() || !it.equals(page.sort(), ignoreCase = true)) {
			""
		} else if ("desc".equals(nextOrder(page, it), ignoreCase = true)) {
			"up"
		} else {
			"down"
		}

	@JvmStatic
	fun readableTotalTime(solution: Solution) = toTime(solution.totalTime)

	/*@JvmStatic
	fun readableTotalTime(solution: SolutionVM) = if (solution.totalTime == null) "" else toTime(solution.totalTime!!)*/

	@JvmStatic
	fun readableTotalTime(time: String) = toTime(time.toLong())

	@JvmStatic
	fun lang(i18n: I18N) = if (i18n.language == "ja") "日本語" else "English"

	@JvmStatic
	fun isCurrentLang(i18n: I18N, lang: String) = i18n.language == lang

	private fun toTime(totalTime: Long) = "%02d:%02d".format(
		TimeUnit.MILLISECONDS.toHours(totalTime),
		TimeUnit.MILLISECONDS.toMinutes(totalTime) % TimeUnit.HOURS.toMinutes(1)
	)
}
