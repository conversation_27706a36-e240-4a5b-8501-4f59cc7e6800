package com.sd.loms.presentation.vm


import com.sd.loms.domain.Location
import com.sd.loms.domain.Page
import jakarta.validation.constraints.*
import java.math.BigDecimal

data class LocationVM(
	val id: Long? = null,
	val uniqueId: String = "",
	@field: NotNull @field: Min(0) @field: Max(1) val type: Int = 0,
	@field: NotBlank val description: String = "",
	@field: NotBlank val address: String = "",
	@field: NotNull val lat: BigDecimal? = 0.toBigDecimal(),
	@field: NotNull val lon: BigDecimal? = 0.toBigDecimal(),
	val isDepot: Boolean = false,
	@field: NotNull @field: Min(0) @field: Max(64800000) val readyTime: Long? = 0,
	@field: NotNull @field: Min(0) @field: Max(70800000) val dueTime: Long? = 0,
	@field: NotNull @field: Min(0) @field: Max(64800000) val serviceDuration: Long? = 0,
) {
	fun toDomainLocation(): Location {
		if (id == null) throw IllegalStateException("Id cannot be null")
		if (lat == null) throw IllegalStateException("Lat cannot be null")
		if (lon == null) throw IllegalStateException("Lon cannot be null")
		if (readyTime == null) throw IllegalStateException("readyTime cannot be null")
		if (dueTime == null) throw IllegalStateException("dueTime cannot be null")
		if (serviceDuration == null) throw IllegalStateException("serviceDuration cannot be null")

		return Location(
			id, uniqueId, type, description, address,   lat, lon,
			isDepot, readyTime, dueTime, serviceDuration
		)
	}

	companion object {
		fun fromDomainLocation(location: Location) =
			with(location) {
				LocationVM(
					id, uniqueId, locationType, description, address,
					lat, lon, depot, readyTime, dueTime, serviceDuration,
				)
			}


		fun fromPage(page: Page<Location>) = Page(page.content.map { fromDomainLocation(it) }, page.total, page.paging)
	}
}
