package com.sd.loms.presentation.controller
/*

import com.sd.loms.application.VehicleDataAccessUseCases
import com.sd.loms.presentation.dto.VehicleForm
import com.sd.loms.presentation.dto.GroupData
import com.sd.loms.domain.Item
import io.quarkus.qute.CheckedTemplate
import io.quarkus.qute.TemplateInstance
import io.quarkus.security.Authenticated
import io.smallrye.common.annotation.Blocking
import jakarta.transaction.Transactional
import jakarta.validation.Valid
import jakarta.ws.rs.*
import jakarta.ws.rs.core.Response
import org.jboss.resteasy.reactive.RestForm
import org.jboss.resteasy.reactive.RestQuery

@Authenticated
@Path("/vehicles")
class Vehicles(
    private val vehicleDataAccess: VehicleDataAccessUseCases
) : UpController() {

    @GET
    @Blocking
    fun index(@RestQuery page: Int = 1, @RestQuery size: Int = 20, @RestQuery sort: String = "id", @RestQuery order: String = "asc"): TemplateInstance {
        val vehicles = vehicleDataAccess.getAllVehicles()
        val vehicleForms = vehicles.map { VehicleForm.fromDomain(it) }
        return Templates.index(vehicleForms, page, size)
    }

    @GET
    @Path("/new")
    @Blocking
    fun addNew(): TemplateInstance {
        val groups = vehicleDataAccess.getAllGroups()
        val items = vehicleDataAccess.getAllItems()
        return Templates.form(VehicleForm(), groups, items, "新規登録")
    }

    @GET
    @Path("/{id}")
    @Blocking
    fun details(@PathParam("id") id: Long): TemplateInstance {
        val vehicle = vehicleDataAccess.getVehicleById(id) ?: throw NotFoundException("Vehicle not found")
        val groups = vehicleDataAccess.getAllGroups()
        val items = vehicleDataAccess.getAllItems()
        val vehicleForm = VehicleForm.fromDomain(vehicle)
        return Templates.form(vehicleForm, groups, items, "編集")
    }

    @POST
    @Transactional
    @Blocking
    fun save(@Valid @RestForm vehicleForm: VehicleForm): Response {
        if (validationFailed()) {
            val groups = vehicleDataAccess.getAllGroups()
            val items = vehicleDataAccess.getAllItems()
            return Response.ok(Templates.form(vehicleForm, groups, items, if (vehicleForm.id == null) "新規登録" else "編集")).build()
        }

        val vehicle = vehicleForm.toDomain()
        if (vehicleForm.id == null) {
            vehicleDataAccess.saveVehicle(vehicle)
            flash("message", "車両を登録しました")
        } else {
            vehicleDataAccess.updateVehicle(vehicle)
            flash("message", "車両を更新しました")
        }

        return renderNothing()
    }

    @DELETE
    @Path("/{id}")
    @Transactional
    @Blocking
    fun delete(@PathParam("id") id: Long): Response {
        vehicleDataAccess.deleteVehicle(id)
        flash("message", "車両を削除しました")
        return renderNothing()
    }

    @CheckedTemplate(requireTypeSafeExpressions = false)
    object Templates {
        @JvmStatic external fun index(vehicles: List<VehicleForm>, page: Int, size: Int): TemplateInstance
        @JvmStatic external fun form(vehicle: VehicleForm, groups: List<GroupData>, items: List<Item>, title: String): TemplateInstance
    }
}
*/
