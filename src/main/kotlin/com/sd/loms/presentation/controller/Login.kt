package com.sd.loms.presentation.controller

import com.sd.loms.application.AuthUseCases
import com.sd.loms.presentation.tech.WebUser
import io.quarkiverse.renarde.router.Router
import io.quarkiverse.renarde.security.ControllerWithUser
import io.quarkus.elytron.security.common.BcryptUtil
import io.quarkus.qute.CheckedTemplate
import io.quarkus.qute.TemplateInstance
import jakarta.validation.constraints.NotBlank
import jakarta.ws.rs.POST
import jakarta.ws.rs.core.Response
import org.jboss.resteasy.reactive.RestForm


class Login(private val authUseCases: AuthUseCases) : ControllerWithUser<WebUser>() {

	fun login() = Templates.login()


	@POST
	fun manualLogin(@RestForm userName: @NotBlank String, @RestForm password: @NotBlank String): Response {
		if (validationFailed()) {
			login()
		}
		val user = authUseCases.findRegisteredByEmail(userName)
		if (user == null
			|| !BcryptUtil.matches(password, user.password)
		) {
			validation.addError("userName", "Invalid username/password")
			prepareForErrorRedirect()
			login()
		}
		val cookie = security.makeUserCookie(WebUser.fromDomainUser(user!!))
		return Response.seeOther(Router.getURI(Solution::index)).cookie(cookie).build()
	}


	//@formatter:off
	@CheckedTemplate
	private object Templates {
		@JvmStatic external fun login   (): TemplateInstance
	}
	//@formatter:on
}
