package com.sd.loms.presentation.controller
/*

import com.sd.loms.application.DriverDataAccessUseCases
import com.sd.loms.application.VehicleDataAccessUseCases
import com.sd.loms.presentation.dto.DriverForm
import com.sd.loms.presentation.dto.VehicleForm
import com.sd.loms.presentation.dto.GroupData
import com.sd.loms.presentation.dto.LocationData
import com.sd.loms.presentation.dto.SalaryData
import com.sd.loms.domain.Item
import io.quarkus.qute.CheckedTemplate
import io.quarkus.qute.TemplateInstance
import io.quarkus.security.Authenticated
import io.smallrye.common.annotation.Blocking
import jakarta.transaction.Transactional
import jakarta.validation.Valid
import jakarta.ws.rs.*
import jakarta.ws.rs.core.Response
import org.jboss.resteasy.reactive.RestForm
import org.jboss.resteasy.reactive.RestQuery

@Authenticated
@Path("/drivers")
class Drivers(
    private val driverDataAccess: DriverDataAccessUseCases,
    private val vehicleDataAccess: VehicleDataAccessUseCases
) : UpController() {

    @GET
    @Blocking
    fun index(@RestQuery page: Int = 1, @RestQuery size: Int = 20, @RestQuery sort: String = "id", @RestQuery order: String = "asc"): TemplateInstance {
        val drivers = driverDataAccess.getAllDrivers()
        val driverForms = drivers.map { DriverForm.fromDomain(it) }
        return Templates.index(driverForms, page, size)
    }

    @GET
    @Path("/new")
    @Blocking
    fun addNew(): TemplateInstance {
        val groups = driverDataAccess.getAllGroups()
        val items = driverDataAccess.getAllItems()
        val vehicles = vehicleDataAccess.getAllVehicles()
        val salaries = driverDataAccess.getAllSalaries()
        val locations = driverDataAccess.getAllLocations()
        return Templates.form(
            DriverForm(),
            groups,
            items,
            vehicles.map { VehicleForm.fromDomain(it) },
            salaries,
            locations,
            "新規登録"
        )
    }

    @GET
    @Path("/{id}")
    @Blocking
    fun details(@PathParam("id") id: Long): TemplateInstance {
        val driver = driverDataAccess.getDriverById(id) ?: throw NotFoundException("Driver not found")
        val groups = driverDataAccess.getAllGroups()
        val items = driverDataAccess.getAllItems()
        val vehicles = vehicleDataAccess.getAllVehicles()
        val salaries = driverDataAccess.getAllSalaries()
        val locations = driverDataAccess.getAllLocations()
        val driverForm = DriverForm.fromDomain(driver)
        return Templates.form(
            driverForm,
            groups,
            items,
            vehicles.map { VehicleForm.fromDomain(it) },
            salaries,
            locations,
            "編集"
        )
    }

    @POST
    @Transactional
    @Blocking
    fun save(@Valid @RestForm driverForm: DriverForm): Response {
        if (validationFailed()) {
            val groups = driverDataAccess.getAllGroups()
            val items = driverDataAccess.getAllItems()
            val vehicles = vehicleDataAccess.getAllVehicles()
            val salaries = driverDataAccess.getAllSalaries()
            val locations = driverDataAccess.getAllLocations()
            return Response.ok(Templates.form(
                driverForm,
                groups,
                items,
                vehicles.map { VehicleForm.fromDomain(it) },
                salaries,
                locations,
                if (driverForm.id == null) "新規登録" else "編集"
            )).build()
        }

        val driver = driverForm.toDomain()
        if (driverForm.id == null) {
            driverDataAccess.saveDriver(driver)
            flash("message", "乗務員を登録しました")
        } else {
            driverDataAccess.updateDriver(driver)
            flash("message", "乗務員を更新しました")
        }

        return renderNothing()
    }

    @DELETE
    @Path("/{id}")
    @Transactional
    @Blocking
    fun delete(@PathParam("id") id: Long): Response {
        driverDataAccess.deleteDriver(id)
        flash("message", "乗務員を削除しました")
        return renderNothing()
    }

    @CheckedTemplate(requireTypeSafeExpressions = false)
    object Templates {
        @JvmStatic external fun index(drivers: List<DriverForm>, page: Int, size: Int): TemplateInstance
        @JvmStatic external fun form(driver: DriverForm, groups: List<GroupData>, items: List<Item>, vehicles: List<VehicleForm>, salaries: List<SalaryData>, locations: List<LocationData>, title: String): TemplateInstance
    }
}
*/
