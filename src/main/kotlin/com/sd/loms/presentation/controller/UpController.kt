package com.sd.loms.presentation.controller


import com.sd.loms.presentation.tech.WebUser
import io.quarkiverse.renarde.security.ControllerWithUser
import io.quarkus.qute.TemplateInstance
import io.quarkus.qute.TemplateInstance.SELECTED_VARIANT
import io.quarkus.qute.Variant
import jakarta.inject.Inject
import jakarta.validation.ConstraintViolation
import jakarta.ws.rs.core.HttpHeaders
import jakarta.ws.rs.core.Response
import org.slf4j.LoggerFactory
import java.math.BigDecimal
import java.util.*
import java.util.stream.Collectors


open class UpController : ControllerWithUser<WebUser>() {

	enum class UpRequestHeader(val key: String) {
		UP_VERSION("X-Up-Version"),
		UP_MODE("X-Up-Mode"),
		UP_FAIL_MODE("X-Up-Fail-Mode"),
		UP_VALIDATE("X-Up-Validate")
	}

	enum class UpResponseHeader(val key: String) {
		UP_LOCATION("X-Up-Location"),
		UP_DISMISS_LAYER("X-Up-Dismiss-Layer")
	}

	private val log = LoggerFactory.getLogger(this.javaClass)

	@Inject
	protected lateinit var httpHeaders: HttpHeaders

	protected fun up(header: UpRequestHeader) = httpHeaders.getHeaderString(header.key)

	protected fun isSubmittedFromModal() = up(UpRequestHeader.UP_FAIL_MODE) == "modal"
	protected fun isUnpolyRequest() = up(UpRequestHeader.UP_VERSION) != null

	protected fun build400Response(template: TemplateInstance): Response {
		val language = i18n.language
		val variant =
			Variant(if (language.startsWith("ja")) Locale.JAPANESE else Locale.ENGLISH, Variant.TEXT_HTML, "UTF-8")
		return Response.status(Response.Status.BAD_REQUEST)
			.entity(template.setAttribute(SELECTED_VARIANT, variant).render()).build()
	}

	protected fun renderNothing(dismissLayer: Boolean = false): Response {
		val res = Response.status(Response.Status.OK)
			.header("X-UP-Target", ":none")
			.build()
		if (dismissLayer) res.headers.add(UpResponseHeader.UP_DISMISS_LAYER.key, "1")
		return res;
	}

	protected fun <T> toErrors(violations: Set<ConstraintViolation<T>>): Map<String, String> {
		return violations.stream()
			.collect(
				Collectors.toMap(
					{ i: ConstraintViolation<T> -> i.propertyPath.toString() },
					{ obj: ConstraintViolation<T> -> obj.message }
				)
			)
	}

	protected fun toBigDecimal(lat: String?): BigDecimal? {
		try {
			return BigDecimal(lat)
		} catch (numberFormatException: NumberFormatException) {
			log.error("numberFormatException")
		}
		return null
	}

	protected fun toLong(lat: String): Long? {
		try {
			return lat.toLong()
		} catch (numberFormatException: NumberFormatException) {
			log.error("numberFormatException")
		}
		return null
	}
}
