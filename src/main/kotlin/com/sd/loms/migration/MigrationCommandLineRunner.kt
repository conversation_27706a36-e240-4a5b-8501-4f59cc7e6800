package com.sd.loms.migration

import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.boot.CommandLineRunner
import org.springframework.jdbc.core.RowMapper
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate
import org.springframework.stereotype.Component
import java.sql.ResultSet

@Component
class MigrationCommandLineRunner(
    @Qualifier("sourceJdbcTemplate") private val source: NamedParameterJdbcTemplate,
    @Qualifier("destinationJdbcTemplate") private val destination: NamedParameterJdbcTemplate,
    @Qualifier("googleJdbcTemplate") private val google: NamedParameterJdbcTemplate
) : CommandLineRunner {

    private val logger = LoggerFactory.getLogger(MigrationCommandLineRunner::class.java)

    override fun run(vararg args: String?) {
        try {
            google.jdbcTemplate.queryForObject("select 'Google'", String::class.java)
            val startTime = System.currentTimeMillis()


            execute(UsersMigration(google, destination))
            execute(PrefecturesMigration(source, destination))
            execute(VehicleTypesMigration(source, destination))
            execute(VehicleCategoriesMigration(destination))
            execute(ItemsMigration(source, destination))

            val prefectures = loadAsMap("select id, roms_id from m_pref")
            val groupLocations = execute(GroupLocationsMigration(source, prefectures)) as List<MLocation>
            val pickupLocations = execute(PickupLocationsMigration(source, prefectures)) as List<MLocation>
            val shipperLocations = execute(ShipperLocationsMigration(source, prefectures)) as List<MLocation>
            val destAreaLocations = execute(DestinationAreaLocationsMigration(source, prefectures)) as List<MLocation>
            val driverLocations = execute(DriverLocationsMigration(source, prefectures)) as List<MLocation>

            val allLocations = groupLocations + pickupLocations + shipperLocations + destAreaLocations + driverLocations
            execute(LocationsMigration(google, destination, allLocations))

            val locations = loadLocations()
            execute(DistancesMigration(google, destination, locations))

            execute(GroupMigration(source, destination, locations))
            execute(PickupsMigration(source, destination, locations))

            val pickups = loadAsMap("select id, roms_id from m_pickups")
            val groups = loadAsMap("select id, roms_id from m_group")
            execute(VehiclesMigration(source, destination, groups))
            execute(SalaryMigration(source, destination))

            val salaries = loadAsMap("select id, roms_id from m_salary")
            val vehicles = loadAsMap("select id, roms_id from m_vehicles")
            execute(DriversMigration(source, destination, locations, groups, salaries, vehicles))
            execute(PickupGroupsMigration(source, destination, pickups, groups))

            execute(ShippersMigration(source, destination, locations))
            execute(DestinationCompaniesMigration(source, destination))

            val companies = loadAsMap("select id, roms_id from m_dest_company")
            execute(DestinationAreasMigration(source, destination, locations, companies))

            val areas = loadAsMap("select id, roms_id from m_dest_area")
            val items = loadAsMap("select id, roms_id from m_items")
            execute(DestinationSpotsMigration(source, destination, areas, items))

            val spots = loadAsMap("select id, roms_id from m_dest_spot")
            val shippers = loadAsMap("select id, roms_id from m_shipper")
            execute(DestinationGroupsMigration(source, destination, spots, groups, shippers))

            execute(OrdersMigration(source, destination, pickups, items))

            val drivers = loadAsMap("select id, roms_id from m_drivers")
            execute(VehicleItemsMigration(destination, vehicles, items))
            execute(DriverItemsMigration(destination, drivers, items))
            execute(DriverVehiclesMigration(destination, drivers, vehicles))
            execute(VehicleTypeAndCapacityUpdateMigration(destination))

            //  TODO check if there are morethan one driver with same name
            //   select snm, count(id) from m_drivers where group_id = 1 and deleted_at is null group by snm having count(id) > 1 order by 2 desc;
            val driverNames =  destination.query("select snm as name, roms_id from m_drivers where group_id = 1 and deleted_at is null") { rs, _ ->
                rs.getString("name") to rs.getInt("roms_id")
            }.toMap()
            val pickupLocationIds = loadAsMap("select location_id as id, roms_id from m_pickups")

            val deliveryLocationIds = loadAsMap("""
                select location_id as id, g.dest_cd as roms_id
                from m_dest_group g
                         inner join main.m_dest_spot s on s.id = g.dest_spot_id
                         inner join main.m_dest_area a on a.id = s.dest_area_id;
            """.trimIndent())

            execute(PatternsMigration(destination, drivers = driverNames, items = items, vehicles = vehicles, shippers = shippers, pickups = pickupLocationIds, deliveries = deliveryLocationIds))
            val endTime = System.currentTimeMillis()
            logger.info("Overall migration process took ${(endTime - startTime) / 1000} seconds")
        } catch (e: Exception) {
            logger.error("Migration process failed", e)
        }
    }

    private fun execute(migration: TableMigration): Any {
        val migrationName = migration::class.simpleName
        val migrationStartTime = System.currentTimeMillis()
        val result = migration.migrate()
        val message = if (result is List<*>) "$migrationName: ${result.size}" else "$migrationName: $result"
        logger.info("$message, took ${System.currentTimeMillis() - migrationStartTime} ms")
        return result
    }

    private fun loadAsMap(query: String) = destination.query(query, toPair).toMap()

    private fun loadLocations() = destination.query("select id, unique_id from m_locations") { rs, _ ->
        rs.getString("unique_id") to rs.getInt("id")
    }.toMap()

    private val toPair = RowMapper { rs: ResultSet, _: Int -> rs.getInt("roms_id") to rs.getInt("id") }
}
