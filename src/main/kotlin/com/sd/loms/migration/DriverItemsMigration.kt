package com.sd.loms.migration

import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate
import org.simpleflatmapper.jdbc.spring.JdbcTemplateMapperFactory as SFM

class DriverItemsMigration(
    private val destination: NamedParameterJdbcTemplate,
    private val drivers: Map<Int, Int>,
    private val items: Map<Int, Int>,
) : TableMigration {

    override fun migrate(): Any {
        val driverItems = mutableListOf<EntityMapping>()

        fun addMappings(id: Int, mappingIds: List<String>) {
            val entity = drivers[id] ?: error("Driver not found for $id")
            mappingIds.forEach {
                driverItems.add(EntityMapping(entity, items[it.toInt()] ?: error("Item missing: $it")))
            }
        }

        fun addMappings(ids: List<Int>, mappingIds: List<String>) = ids.forEach { addMappings(it, mappingIds) }

        addMappings(
            1, listOf(
                "00001", "00002", "00004", "00005", "00007", "00008", "00015", "00018", "00019", "00020",
                "00021", "00024", "00025", "00060", "00110", "10009", "10013", "10015", "20429", "20441",
                "20442", "60463", "60464", "60474", "60475", "60477", "60478", "60481", "60483", "60485",
            )
        )

        addMappings(
            listOf(7, 9), listOf(
                "00001", "00002", "00004", "00005", "00007", "00008", "00015", "00018", "00019", "00024",
                "00025", "00060", "00110", "10009", "10013", "10015", "20429", "20441", "20442", "60463",
                "60464", "60474", "60475", "60477", "60478", "60481", "60483", "60485",
            )
        )

        addMappings(
            listOf(14, 26), listOf(
                "00001", "00002", "00004", "00005", "00007", "00008", "00010", "00011", "00015", "00018", "00019",
                "00024", "00025", "00035", "00058", "00060", "00110", "10009", "10013", "10015", "20429", "20441",
                "20442", "60463", "60464", "60474", "60475", "60477", "60478", "60481", "60483", "60485",
            )
        )

        addMappings(
            18, listOf(
                "00001", "00002", "00004", "00005", "00007", "00008", "00010", "00015", "00018", "00019", "00024",
                "00025", "00035", "00060", "00110", "10009", "10013", "10015", "20429", "20441", "20442", "60463",
                "60464", "60474", "60475", "60477", "60478", "60481", "60483", "60485",
            )
        )

        addMappings(
            24, listOf(
                "00001", "00002", "00004", "00005", "00007", "00008", "00010", "00015", "00018", "00019", "00024",
                "00025", "00035", "00058", "00060", "00110", "10009", "10013", "10015", "20429", "20441", "20442",
                "60463", "60464", "60474", "60475", "60477", "60478", "60481", "60483", "60485",
            )
        )

        addMappings(
            155, listOf(
                "00001", "00002", "00004", "00005", "00007", "00008", "00010", "00011", "00015", "00018", "00019",
                "00024", "00025", "00035", "00058", "00060", "00110", "10009", "10013", "10015", "20369", "20428",
                "20429", "20441", "20442", "60463", "60464", "60474", "60475", "60477", "60478", "60481", "60483",
                "60485",
            )
        )

        addMappings(
            listOf(166, 187), listOf(
                "00001", "00002", "00004", "00005", "00007", "00008", "00015", "00018", "00019", "00024", "00025",
                "00060", "00110", "10009", "10013", "10015", "20369", "20428", "20429", "20441", "20442", "60463",
                "60464", "60474", "60475", "60477", "60478", "60481", "60483", "60485",
            )
        )

        addMappings(
            193, listOf(
                "00001", "00002", "00004", "00005", "00007", "00008", "00015", "00024", "00025", "00110", "10015",
                "60464",
            )
        )

        addMappings(
            217, listOf(
                "00001", "00002", "00004", "00005", "00007", "00008", "00010", "00015", "00018", "00019", "00020",
                "00021", "00024", "00025", "00058", "00060", "00110", "10009", "10013", "10015", "20429", "20441",
                "20442", "60463", "60464", "60474", "60475", "60477", "60478", "60481", "60483", "60485",
            )
        )

        addMappings(233, listOf("00010", "00011", "00035", "60465"))

        addMappings(
            258, listOf(
                "00001", "00002", "00004", "00005", "00007", "00008", "00010", "00015", "00018", "00019", "00024",
                "00025", "00060", "00110", "10009", "10013", "10015", "20369", "20428", "20429", "20441", "20442",
                "60463", "60464", "60467", "60474", "60475", "60477", "60478", "60481", "60483", "60485",
            )
        )

        addMappings(
            listOf(261, 338), listOf(
                "00001", "00002", "00004", "00005", "00007", "00008", "00010", "00015", "00018", "00019", "00020",
                "00021", "00024", "00025", "00060", "00110", "10009", "10013", "10015", "20429", "20441", "20442",
                "60463", "60464", "60474", "60475", "60477", "60478", "60481", "60483", "60485",
            )
        )

        addMappings(
            271, listOf(
                "00001", "00002", "00004", "00005", "00007", "00008", "00010", "00015", "00018", "00019", "00024",
                "00025", "00035", "00060", "00067", "00110", "10009", "10013", "10015", "20369", "20428", "20429",
                "20441", "20442", "60463", "60464", "60465", "60474", "60475", "60477", "60478", "60481", "60483",
                "60485", "60486",
            )
        )

        addMappings(
            278, listOf(
                "00001", "00002", "00004", "00005", "00007", "00008", "00010", "00015", "00018", "00024", "00025",
                "00035", "00060", "00067", "00110", "10009", "10013", "10015", "20429", "20441", "20442", "60463",
                "60464", "60465", "60474", "60475", "60477", "60478", "60481", "60483", "60485", "60486",
            )
        )

        addMappings(
            listOf(280, 287), listOf(
                "00001", "00002", "00004", "00005", "00007", "00008", "00010", "00015", "00018", "00019", "00020",
                "00021", "00024", "00025", "00060", "00110", "10009", "10013", "10015", "20398", "20429", "20441",
                "20442", "60463", "60464", "60474", "60475", "60477", "60478", "60481", "60483", "60485",
            )
        )

        addMappings(282, listOf("00018", "00019"))

        addMappings(
            286, listOf(
                "00001", "00002", "00004", "00005", "00007", "00008", "00010", "00015", "00018", "00019", "00024",
                "00025", "00035", "00058", "00060", "00110", "10009", "10013", "10015", "20403", "20429", "20441",
                "20442", "60463", "60464", "60465", "60474", "60475", "60477", "60478", "60481", "60483", "60485",
            )
        )

        addMappings(
            293, listOf(
                "00001", "00002", "00004", "00005", "00007", "00008", "00010", "00015", "00018", "00019", "00024",
                "00025", "00035", "00060", "00110", "10009", "10013", "10015", "20369", "20403", "20428", "20429",
                "20441", "20442", "60463", "60464", "60465", "60467", "60474", "60475", "60477", "60478", "60481",
                "60483", "60485",
            )
        )

        addMappings(
            318, listOf(
                "00001", "00002", "00004", "00005", "00007", "00008", "00010", "00011", "00015", "00018", "00019",
                "00024", "00025", "00035", "00060", "00067", "00110", "10009", "10013", "10015", "20429", "20441",
                "20442", "60463", "60464", "60465", "60474", "60475", "60477", "60478", "60481", "60483", "60485",
                "60486",
            )
        )

        addMappings(342, listOf("00010", "00035", "60465"))

        addMappings(350, listOf("00001", "00002", "00004", "00005", "00007", "00008", "00010", "00015"))

        addMappings(
            361, listOf(
                "00001", "00002", "00004", "00005", "00007", "00008", "00010", "00015", "00018", "00019", "00020",
                "00021", "00024", "00025", "00035", "00060", "00067", "00110", "10009", "10013", "10015", "20429",
                "20441", "20442", "60463", "60464", "60465", "60474", "60475", "60477", "60478", "60481", "60483",
                "60485", "60486",
            )
        )

        addMappings(
            381, listOf(
                "00001", "00002", "00004", "00005", "00007", "00008", "00010", "00015", "00018", "00019", "00024",
                "00025", "00060", "00067", "00110", "10009", "10013", "10015", "20429", "20441", "20442", "60463",
                "60464", "60465", "60474", "60475", "60477", "60478", "60481", "60483", "60485", "60486",
            )
        )

        addMappings(
            403, listOf(
                "00001", "00002", "00004", "00005", "00007", "00008", "00015", "00018", "00019", "00024", "00025",
                "00060", "00110", "10009", "10013", "10015", "20398", "20403", "20429", "20441", "20442", "60463",
                "60464", "60474", "60475", "60477", "60478", "60481", "60483", "60485",
            )
        )


        addMappings(408, listOf("00001", "00002", "00004", "00005", "00007", "00008", "00010", "00015"))
        addMappings(listOf(409, 419), listOf("00010"))
        addMappings(listOf(416, 422, 423), listOf("00001", "00002", "00004", "00005", "00007", "00008", "00015"))
        val batch = SFM.newInstance().newSqlParameterSourceFactory(EntityMapping::class.java)
            .newSqlParameterSources(driverItems)

        val insertSql = "insert into driver_items (driver_id, item_id) values (:entityId, :mappingId)"
        val result = destination.batchUpdate(insertSql, batch)


        return result.size
    }
}
