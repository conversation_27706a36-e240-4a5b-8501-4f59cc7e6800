package com.sd.loms.migration

import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate
import kotlin.collections.map
import org.simpleflatmapper.jdbc.spring.JdbcTemplateMapperFactory as SFM

class DestinationSpotsMigration(
    private val source: NamedParameterJdbcTemplate,
    private val destination: NamedParameterJdbcTemplate,
    private val areas: Map<Int, Int>,
    private val items: Map<Int, Int>
) : TableMigration {
    override fun migrate(): Any {
        val selectSql = """
                select
                    da_no destAreaId, nm, spot, case when item_cd ~ '^[0-9]+${'$'}' then cast(item_cd as integer) end item_id, kana1, kana2, snm1, snm2,
                    dept1, dept2, person1, person2, contact1, contact2, cmt1_flg, cmt1,
                    cmt2_flg, cmt2, cmt3_flg, cmt3, cmt4_flg, cmt4, cmt5_flg, cmt5,
                    cmt6_flg, cmt6, cmt7_flg, cmt7, cmt8_flg, cmt8, cmt9_flg, cmt9,
                    cmt10_flg, cmt10, file_id, file1, file2, remarks,
                    cast(ds_no as integer) roms_id,
                    case when delete_flg = true then current_timestamp else null end deleted_at
                from m_dest_spot order by roms_id;
            """.trimIndent()

        val insertSql = """
                insert into m_dest_spot (dest_area_id, nm, spot, item_id, kana1, kana2, snm1, snm2, dept1, dept2, person1, person2,
                        contact1, contact2, cmt1_flg, cmt1, cmt2_flg, cmt2, cmt3_flg, cmt3, cmt4_flg, cmt4, cmt5_flg,
                        cmt5, cmt6_flg, cmt6, cmt7_flg, cmt7, cmt8_flg, cmt8, cmt9_flg, cmt9, cmt10_flg, cmt10, file_id,
                        file1, file2, remarks, roms_id, deleted_at)
                VALUES (:destAreaId, :nm, :spot, :itemId, :kana1, :kana2, :snm1, :snm2, :dept1, :dept2, :person1, :person2,
                        :contact1, :contact2, :cmt1Flg, :cmt1, :cmt2Flg, :cmt2, :cmt3Flg, :cmt3, :cmt4Flg, :cmt4, :cmt5Flg,
                        :cmt5, :cmt6Flg, :cmt6, :cmt7Flg, :cmt7, :cmt8Flg, :cmt8, :cmt9Flg, :cmt9, :cmt10Flg, :cmt10, :fileId,
                        :file1, :file2, :remarks, :romsId, :deletedAt)
            """.trimIndent()

        val data = source.query(selectSql, SFM.newInstance().newRowMapper(MDestSpot::class.java)).map {
            it.copy(
                destAreaId = areas[it.destAreaId] ?: error("Area not found for ${it.destAreaId}"),
                itemId = items[it.itemId]
            )
        }

        val batch = SFM.newInstance().newSqlParameterSourceFactory(MDestSpot::class.java).newSqlParameterSources(data)
        destination.batchUpdate(insertSql, batch)
        return data
    }

}