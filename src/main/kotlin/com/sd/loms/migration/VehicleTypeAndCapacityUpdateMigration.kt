package com.sd.loms.migration

import org.springframework.jdbc.core.RowMapper
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate
import java.sql.ResultSet
import kotlin.collections.toMap
import org.simpleflatmapper.jdbc.spring.JdbcTemplateMapperFactory as SFM

class VehicleTypeAndCapacityUpdateMigration(
    private val destination: NamedParameterJdbcTemplate
) : TableMigration {
    private val vehiclesPSF = SFM.newInstance().newSqlParameterSourceFactory(VehicleTypeAndCapacity::class.java)


    override fun migrate(): Any {

        val categories = loadAsMap("select id, name from m_vehicle_categories")
        val weightCategories = loadAsMap("select id, name from m_vehicle_weight_categories")
        val tanks = loadAsMap("select id, name from m_vehicle_tank_materials")
        val alkalineAcidic = loadAsMap("select id, name from m_vehicle_alkaline")

        val catOth = categories["その他"] ?: error("category not found for その他")
        val catSusLorry = categories["SUSローリー車"] ?: error("category not found for SUSローリー車")
        val catTankCarrier = categories["タンク積載車"] ?: error("category not found for タンク積載車")


        val wCat4T = weightCategories["４ｔ"] ?: error("weight category not found for ４ｔ")
        val wCat10T = weightCategories["１０ｔ"] ?: error("weight category not found for １０ｔ")
        val wCatOth = weightCategories["その他"] ?: error("weight category not found for その他")

        val tRokotan = tanks["ロコタン"] ?: error("tank material not found for ロコタン")
        val tRubberLining = tanks["ゴムライニング"] ?: error("tank material not found for ゴムライニング")
        val tSuperFlowLining = tanks["スーパーフローライニング"] ?: error("tank material not found for スーパーフローライニング")
        val tSUS = tanks["SUS"] ?: error("tank material not found for SUS")
        val tISOContainer = tanks["ISOコンテナ"] ?: error("tank material not found for ISOコンテナ")
        val tTeflonLining = tanks["テフロンライニング"] ?: error("tank material not found for テフロンライニング")
        val tFRP = tanks["FRP"] ?: error("tank material not found for FRP")


        val alkaline = alkalineAcidic["アルカリ性"] ?: error("alkaline not found for アルカリ性")
        val acidic = alkalineAcidic["酸性"] ?: error("alkaline not found for 酸性")


        val vehicles = listOf(
            VehicleTypeAndCapacity(1, catOth, wCat4T, tRokotan, alkaline, 1300, 1950, 3250, 3490),
            VehicleTypeAndCapacity(17, catOth, wCat10T, tRubberLining, acidic, 0, 0, 9500, 10730),
            VehicleTypeAndCapacity(20, catOth, wCat10T, tRubberLining, acidic, 5800, 3500, 9300, 10850),
            VehicleTypeAndCapacity(160, catOth, wCat10T, tSuperFlowLining, alkaline, 0, 0, 10000, 10800),
            VehicleTypeAndCapacity(164, catSusLorry, wCat10T, tSUS, alkaline, 0, 0, 8000, 21940),
            VehicleTypeAndCapacity(180, catOth, wCatOth, tFRP, acidic, 0, 0, 13200, 14120),
            VehicleTypeAndCapacity(186, catTankCarrier, wCat10T, tISOContainer, alkaline, 0, 0, 9500, 13800),
            VehicleTypeAndCapacity(223, catSusLorry, wCat4T, tSUS, alkaline, 0, 0, 3700, 5180),
            VehicleTypeAndCapacity(228, catSusLorry, wCatOth, tSUS, alkaline, 0, 0, 11600, 14030),
            VehicleTypeAndCapacity(230, catOth, wCat10T, tRubberLining, acidic, 5500, 3500, 9000, 10350),
            VehicleTypeAndCapacity(239, catSusLorry, wCat10T, tSUS, alkaline, 0, 0, 8400, 11500),
            VehicleTypeAndCapacity(250, catOth, wCat10T, tRubberLining, acidic, 5500, 3500, 9000, 10920),
            VehicleTypeAndCapacity(261, catOth, wCat10T, tRubberLining, acidic, 5500, 3500, 9000, 10920),
            VehicleTypeAndCapacity(273, catSusLorry, wCat10T, tSUS, alkaline, 4500, 4400, 8900, 11570),

            VehicleTypeAndCapacity(279, catTankCarrier, wCat10T, tISOContainer, alkaline, 0, 0, 9500, 13600),
            VehicleTypeAndCapacity(287, catTankCarrier, wCat10T, tISOContainer, alkaline, 0, 0, 9500, 13800),
            VehicleTypeAndCapacity(288, catSusLorry, wCat10T, tSUS, alkaline, 0, 0, 9500, 10410),
            VehicleTypeAndCapacity(304, catSusLorry, wCat10T, tSUS, alkaline, 0, 0, 8000, 12000),
            VehicleTypeAndCapacity(307, catSusLorry, wCat10T, tSUS, alkaline, 0, 0, 7700, 11550),
            VehicleTypeAndCapacity(315, catSusLorry, wCat10T, tSUS, alkaline, 0, 0, 7500, 11700),
            VehicleTypeAndCapacity(318, catOth, wCat10T, tRubberLining, acidic, 0, 0, 9100, 10460),
            VehicleTypeAndCapacity(326, catSusLorry, wCat10T, tSUS, alkaline, 0, 0, 8700, 11480),
            VehicleTypeAndCapacity(330, catTankCarrier, wCat10T, tISOContainer, alkaline, 0, 0, 9500, 13800),
            VehicleTypeAndCapacity(336, catSusLorry, wCat10T, tSUS, alkaline, 0, 0, 7900, 12000),
            VehicleTypeAndCapacity(337, catOth, wCat10T, tTeflonLining, alkaline, 0, 0, 9500, 11110),
            VehicleTypeAndCapacity(341, catSusLorry, wCat10T, tSUS, alkaline, 0, 0, 8800, 11520),
            VehicleTypeAndCapacity(344, catSusLorry, wCatOth, tSUS, alkaline, 0, 0, 9500, 13960),
            VehicleTypeAndCapacity(359, catSusLorry, wCat10T, tSUS, alkaline, 0, 0, 8700, 11350),
            VehicleTypeAndCapacity(363, catSusLorry, wCat10T, tSUS, alkaline, 0, 0, 9000, 11520),
            VehicleTypeAndCapacity(364, catSusLorry, wCat10T, tSUS, alkaline, 4200, 4300, 8500, 11360),
            VehicleTypeAndCapacity(365, catOth, wCat10T, tRubberLining, acidic, 0, 0, 9200, 10480),
            VehicleTypeAndCapacity(366, catSusLorry, wCat10T, tSUS, alkaline, 4100, 4200, 8300, 11690),
            VehicleTypeAndCapacity(370, catSusLorry, wCat10T, tSUS, alkaline, 0, 0, 8800, 11880),
            VehicleTypeAndCapacity(371, catSusLorry, wCat10T, tSUS, alkaline, 4500, 4400, 8900, 11570),
            VehicleTypeAndCapacity(372, catSusLorry, wCat10T, tSUS, alkaline, 0, 0, 8700, 11740),
            VehicleTypeAndCapacity(375, catSusLorry, wCatOth, tSUS, alkaline, 0, 0, 12000, 14040),
            VehicleTypeAndCapacity(380, catSusLorry, wCat10T, tSUS, alkaline, 4500, 4400, 8900, 11570),

            )

        destination.batchUpdate(
            """
            update m_vehicles set 
                vehicle_category = :category,
                weight_category = :weightCategory,
                tank_material = :tankMaterial,
                alkaline = :alkaline,
                maximum_load_capacity = :maximumLoadCapacity,
                front_tank_capacity = :frontTankCapacity,
                rear_tank_capacity = :rearTankCapacity,
                total_tank_capacity = :totalCapacity
             where roms_id = :romsId
        """.trimMargin(), vehiclesPSF.newSqlParameterSources(vehicles)
        )


        return vehicles
    }

    private fun loadAsMap(query: String) = destination.query(query, toPair).toMap()

    private val toPair = RowMapper { rs: ResultSet, _: Int -> rs.getString("name") to rs.getInt("id") }
}
