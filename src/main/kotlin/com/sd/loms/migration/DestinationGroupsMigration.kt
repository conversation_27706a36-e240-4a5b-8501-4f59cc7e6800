package com.sd.loms.migration


import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate
import kotlin.collections.map
import org.simpleflatmapper.jdbc.spring.JdbcTemplateMapperFactory as SFM

class DestinationGroupsMigration(
    private val source: NamedParameterJdbcTemplate,
    private val destination: NamedParameterJdbcTemplate,
    private val spots: Map<Int, Int>,
    private val groups: Map<Int, Int>,
    private val shippers: Map<Int, Int>
) : TableMigration {
    override fun migrate(): Any {
        val selectSql = """
            select
                   ds_no destSpotId, g_no groupId, dest_cd, shipper_cd shipperId, keisu, 
                   fare_type, fare_dist, fare_price, remarks, cast(dg_no as integer) roms_id, 
                   case when delete_flg = true then current_timestamp else null end deleted_at
            from m_dest_group order by roms_id;
        """.trimIndent()

        val insertSql = """
            insert into m_dest_group (
                dest_spot_id, group_id, dest_cd, shipper_id, keisu, fare_type, fare_dist, fare_price,
                remarks, roms_id, deleted_at
            ) VALUES (
                :destSpotId, :groupId, :destCd, :shipperId, :keisu, :fareType, :fareDist, :farePrice,
                :remarks, :romsId, :deletedAt
            )
        """.trimIndent()

        val data = source.query(selectSql, SFM.newInstance().newRowMapper(MDestGroup::class.java)).map {
            it.copy(
                destSpotId = spots[it.destSpotId] ?: error("Spot not found for ${it.destSpotId}"),
                groupId = groups[it.groupId] ?: error("Group not found for ${it.groupId}"),
                shipperId = shippers[it.shipperId] ?: error("Shipper not found for ${it.shipperId}")
            )
        }

        val batch = SFM.newInstance().newSqlParameterSourceFactory(MDestGroup::class.java).newSqlParameterSources(data)
        destination.batchUpdate(insertSql, batch)
        return data
    }
}
