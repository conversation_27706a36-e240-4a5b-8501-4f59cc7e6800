package com.sd.loms.migration


import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate
import java.time.LocalDate
import kotlin.collections.map
import org.simpleflatmapper.jdbc.spring.JdbcTemplateMapperFactory as SFM

class OrdersMigration(
    private val source: NamedParameterJdbcTemplate,
    private val destination: NamedParameterJdbcTemplate,
    private val pickups: Map<Int, Int>,
    private val items: Map<Int, Int>
) : TableMigration {

    override fun migrate(): Any {
        val selectSql = """
            select
                type_no, import_key, o.dest_cd, item_cd item_id, pickup_cd pickup_id, volume1, unit1,
                volume2, unit2, requirement, ship_date as ship_date_str, deli_date as deli_date_str, set_time, cmt1, cmt2,
                o.remarks, order_status, p_no, opt1, opt2, opt3, o.order_id roms_id, lock_u_id,
                case when o.delete_flg = true then current_timestamp else null end deleted_at
            from t_order o left join m_dest_group dg on o.dest_cd = dg.dest_cd and dg.delete_flg = false and dg.g_no = 1 where deli_date > '2025-03-01' order by o.regist_ymd;
        """.trimIndent()

        val insertSql = """
            insert into t_orders(
                type_no, import_key, dest_cd, item_id, pickup_id, volume1, unit1, volume2,
                unit2, requirement, ship_date, deli_date, set_time, cmt1, cmt2, remarks,
                order_status, p_no, opt1, opt2, opt3, roms_id, lock_u_id, deleted_at
            ) VALUES (
                :typeNo, :importKey, :destCd, :itemId, :pickupId, :volume1, :unit1, :volume2,
                :unit2, :requirement, :shipDate, :deliDate, :setTime, :cmt1, :cmt2, :remarks,
                :orderStatus, :pNo, :opt1, :opt2, :opt3, :romsId, :lockUId, :deletedAt
            )
        """.trimIndent()

        val orders = source.query(selectSql, SFM.newInstance().newRowMapper(Order::class.java)).map {

            val shipDate = it.shipDateStr?.let { date-> LocalDate.parse(date) }
            val deliDate = it.deliDateStr?.let { date-> LocalDate.parse(date) }

            it.copy(
                pickupId = pickups[it.pickupId] ?: error("Pickup not found for ${it.pickupId}"),
                itemId = items[it.itemId] ?: error("Item not found for ${it.itemId}"),
                shipDate = shipDate,
                deliDate = deliDate,
            )
        }
        val batch = SFM.newInstance().newSqlParameterSourceFactory(Order::class.java).newSqlParameterSources(orders)
        destination.batchUpdate(insertSql, batch)
        return orders
    }

}
