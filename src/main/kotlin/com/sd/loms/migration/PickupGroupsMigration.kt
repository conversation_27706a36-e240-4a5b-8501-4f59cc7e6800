package com.sd.loms.migration

import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate
import kotlin.collections.map
import org.simpleflatmapper.jdbc.spring.JdbcTemplateMapperFactory as SFM

class PickupGroupsMigration(
    private val source: NamedParameterJdbcTemplate,
    private val destination: NamedParameterJdbcTemplate,
    private val pickups: Map<Int, Int>,
    private val groups: Map<Int, Int>
) : TableMigration {
    override fun migrate(): Any {
        val sql = "select cast(pickup_cd as integer) pickup_id, g_no group_id from m_pickup_group order by pickup_id;"
        val insertSql = "insert into m_pickup_group (pickup_id, group_id) VALUES (:pickupId, :groupId)"

        val pickupGroups = source.query(sql, SFM.newInstance().newRowMapper(MPickupGroup::class.java)).map {
            it.copy(
                pickupId = pickups[it.pickupId] ?: error("Pickup not found for ${it.pickupId}"),
                groupId = groups[it.groupId] ?: error("Group not found for ${it.groupId}")
            )
        }

        val batch = SFM.newInstance().newSqlParameterSourceFactory(MPickupGroup::class.java)
            .newSqlParameterSources(pickupGroups)
        destination.batchUpdate(insertSql, batch)
        return pickupGroups
    }
}