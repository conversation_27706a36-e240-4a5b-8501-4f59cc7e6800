package com.sd.loms.migration


import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate
import kotlin.collections.map
import org.simpleflatmapper.jdbc.spring.JdbcTemplateMapperFactory as SFM

class ShippersMigration(
    private val source: NamedParameterJdbcTemplate,
    private val destination: NamedParameterJdbcTemplate,
    private val locations: Map<String, Int>
) : TableMigration {

    override fun migrate(): Any {
        val selectSql = """
                select charge_cd, nm, kana, snm1, snm2, concat('m_shipper_', cast(shipper_cd as integer)) location_id, 
                opt1, opt2, opt3, opt4, opt5, cast(shipper_cd as integer) roms_id, 
                case when delete_flg = true then current_timestamp else null end deleted_at
                from m_shipper order by roms_id;
            """.trimIndent()

        val insertSql = """
            insert into m_shipper (
                charge_cd, nm, kana, snm1, snm2, location_id, opt1, opt2, opt3, opt4, opt5, roms_id, deleted_at
            ) VALUES (
                :chargeCd, :nm, :kana, :snm1, :snm2, :locationId, :opt1, :opt2, :opt3, :opt4, :opt5, :romsId, :deletedAt
            )
        """.trimIndent()

        val shippers = source.query(selectSql, SFM.newInstance().newRowMapper(Shipper::class.java))
            .map { it.copy(locationId = locations[it.locationId].toString()) }
        val batch = SFM.newInstance().newSqlParameterSourceFactory(Shipper::class.java).newSqlParameterSources(shippers)
        destination.batchUpdate(insertSql, batch)
        return shippers
    }
}

