package com.sd.loms.migration

import org.slf4j.LoggerFactory
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate
import org.simpleflatmapper.jdbc.spring.JdbcTemplateMapperFactory as SFM

class DriverVehiclesMigration(
    private val destination: NamedParameterJdbcTemplate,
    private val drivers: Map<Int, Int>,
    private val vehicles: Map<Int, Int>,
) : TableMigration {

    private val logger = LoggerFactory.getLogger(DriverVehiclesMigration::class.java)

    override fun migrate(): Any {
        val driverVehicles = mutableListOf<EntityMapping>()
        val driverPrimaryVehicleMapping = mutableListOf<EntityMapping>()

        fun addMappings(id: Int, mappingIds: List<String>) {
            val entity = drivers[id] ?: error("Driver not found for $id")
            mappingIds.forEach {
                driverVehicles.add(EntityMapping(entity, vehicles[it.toInt()] ?: error("Vehicle missing: $it")))
            }
        }

        fun addMappings(ids: List<Int>, mappingIds: List<String>) = ids.forEach { addMappings(it, mappingIds) }

        fun addPrimaryVehicleMapping(id: Int, mappingId: Int) {
            val driver = drivers[id] ?: error("Driver not found for $id")
            val vehicle = vehicles[mappingId] ?: error("Vehicle missing: $mappingId")
            driverPrimaryVehicleMapping.add(EntityMapping(driver, vehicle))
        }

        addMappings(
            listOf(
                1, 7, 9, 14, 18, 24, 115, 166, 187, 217, 233, 258, 261, 271, 278, 280, 286, 287, 293, 318, 338, 342,
                350, 361, 381, 403, 408, 409, 416, 419, 422, 423
            ),
            listOf(
                "00001", "00017", "00020", "00160", "00180", "00186", "00223", "00228", "00230", "00239",
                "00250", "00261", "00273", "00279", "00287", "00304", "00307", "00315", "00318", "00326",
                "00330", "00336", "00337", "00341", "00344", "00359", "00363", "00364", "00365", "00366",
                "00370", "00371", "00372", "00375", "00380"
            )
        )

        addMappings(26, listOf("00001"))
        addMappings(193, listOf("00164"))
        addMappings(282, listOf("00288"))

        //1, 14, 24, 166, 187, 286, 293, 381

        addPrimaryVehicleMapping(7, 307)
        addPrimaryVehicleMapping(9, 315)
        addPrimaryVehicleMapping(18, 359)
        addPrimaryVehicleMapping(26, 1)
        addPrimaryVehicleMapping(115, 330)
        addPrimaryVehicleMapping(193, 164)
        addPrimaryVehicleMapping(217, 239)
        addPrimaryVehicleMapping(233, 261)
        addPrimaryVehicleMapping(258, 279)
        addPrimaryVehicleMapping(261, 326)
        addPrimaryVehicleMapping(271, 337)
        addPrimaryVehicleMapping(278, 365)
        addPrimaryVehicleMapping(280, 363)
        addPrimaryVehicleMapping(282, 288)
        addPrimaryVehicleMapping(287, 341)
        addPrimaryVehicleMapping(318, 230)
        addPrimaryVehicleMapping(338, 364)
        addPrimaryVehicleMapping(342, 20)
        addPrimaryVehicleMapping(350, 344)
        addPrimaryVehicleMapping(361, 366)
        addPrimaryVehicleMapping(403, 160)
        addPrimaryVehicleMapping(408, 370)
        addPrimaryVehicleMapping(409, 318)
        addPrimaryVehicleMapping(416, 228)
        addPrimaryVehicleMapping(419, 250)
        addPrimaryVehicleMapping(422, 375)
        addPrimaryVehicleMapping(423, 380)


        val batch = SFM.newInstance().newSqlParameterSourceFactory(EntityMapping::class.java)
            .newSqlParameterSources(driverVehicles)

        val insertSql = "insert into driver_vehicles (driver_id, vehicle_id) values (:entityId, :mappingId)"
        val result = destination.batchUpdate(insertSql, batch)

        val primaryBatch = SFM.newInstance().newSqlParameterSourceFactory(EntityMapping::class.java)
            .newSqlParameterSources(driverPrimaryVehicleMapping)

        val primaryInsertSql = "update m_drivers set vehicle_id = :mappingId where id= :entityId"
        val primaryResult = destination.batchUpdate(primaryInsertSql, primaryBatch)

        val checkSql = """
            SELECT vehicle_id, count(*) as count
            FROM m_drivers t
            WHERE vehicle_id is not null
              and group_id = 1
              and deleted_at is null
            group by vehicle_id having count(*) > 1
            order by 2 desc;
        """.trimIndent()

        if (primaryResult.size != driverPrimaryVehicleMapping.size) {
            logger.error("Primary vehicle update mismatch ${driverPrimaryVehicleMapping.size} != ${primaryResult.size}")
        }
        return result.size
    }
}
