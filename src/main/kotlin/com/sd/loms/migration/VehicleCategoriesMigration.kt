package com.sd.loms.migration

import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate
import org.simpleflatmapper.jdbc.spring.JdbcTemplateMapperFactory as SFM

class VehicleCategoriesMigration(
    private val destination: NamedParameterJdbcTemplate
) : TableMigration {
    private val vehicleCategoryPSF = SFM.newInstance().newSqlParameterSourceFactory(VehicleCategory::class.java)
    private val vehicleWeightCategoryPSF = SFM.newInstance().newSqlParameterSourceFactory(VehicleWeightCategory::class.java)
    private val vehicleTankMaterialPSF = SFM.newInstance().newSqlParameterSourceFactory(VehicleTankMaterial::class.java)
    private val vehicleAlkalinePSF = SFM.newInstance().newSqlParameterSourceFactory(VehicleAlkaline::class.java)


    override fun migrate(): Any {
        val categories = listOf(
            VehicleCategory("SUSローリー車", 1),
            VehicleCategory("特殊ローリー車", 2),
            VehicleCategory("タンク積載車", 3),
            VehicleCategory("液化酸素ローリー", 4),
            VehicleCategory("液化窒素ローリー", 5),
            VehicleCategory("液化アルゴンローリー", 6),
            VehicleCategory("液化炭酸", 7),
            VehicleCategory("トレーラ", 8),
            VehicleCategory("ウイングトラック", 9),
            VehicleCategory("平ボディー", 10),
            VehicleCategory("その他", 11),
        )
        destination.batchUpdate(
            """
            insert into m_vehicle_categories (name, display_order, deleted_at) values 
            (:name, :sort, :deleted_at)
        """.trimMargin(), vehicleCategoryPSF.newSqlParameterSources(categories)
        )

        val weightCategories = listOf(
            VehicleWeightCategory("２ｔ", 1),
            VehicleWeightCategory("４ｔ", 2),
            VehicleWeightCategory("１０ｔ", 3),
            VehicleWeightCategory("１１ｔ", 4),
            VehicleWeightCategory("トレーラ", 5),
            VehicleWeightCategory("２ｔトラック", 6),
            VehicleWeightCategory("４ｔトラック", 7),
            VehicleWeightCategory("１０ｔトラック", 8),
            VehicleWeightCategory("特殊車両", 9),
            VehicleWeightCategory("その他", 10),
        )
        destination.batchUpdate(
            """
            insert into m_vehicle_weight_categories (name, display_order, deleted_at) values 
            (:name, :sort, :deleted_at)
        """.trimMargin(), vehicleWeightCategoryPSF.newSqlParameterSources(weightCategories)
        )


        val tankMaterial = listOf(
            VehicleTankMaterial("FRP", 1),
            VehicleTankMaterial("ISOコンテナ", 2),
            VehicleTankMaterial("SUS", 3),
            VehicleTankMaterial("ゴムライニング", 4),
            VehicleTankMaterial("スーパーフローライニング", 5),
            VehicleTankMaterial("テフロンライニング", 6),
            VehicleTankMaterial("ロコタン", 7),
        )
        destination.batchUpdate(
            """
            insert into m_vehicle_tank_materials (name, display_order, deleted_at) values 
            (:name, :sort, :deleted_at)
        """.trimMargin(), vehicleTankMaterialPSF.newSqlParameterSources(tankMaterial)
        )

        val vehicleAlkaline = listOf(
            VehicleAlkaline("アルカリ性", 1),
            VehicleAlkaline("酸性", 2),
        )
        destination.batchUpdate(
            """
            insert into m_vehicle_alkaline (name, display_order, deleted_at) values 
            (:name, :sort, :deleted_at)
        """.trimMargin(), vehicleAlkalinePSF.newSqlParameterSources(vehicleAlkaline)
        )


        return categories
    }
}
