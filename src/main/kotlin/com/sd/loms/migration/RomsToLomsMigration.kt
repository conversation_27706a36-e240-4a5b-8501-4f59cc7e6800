package com.sd.loms.migration

import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.jdbc.core.RowMapper
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate
import java.sql.ResultSet
import org.simpleflatmapper.jdbc.spring.JdbcTemplateMapperFactory as SFM


/*@Component*/
class RomsToLomsMigration(
    @Qualifier(value = "sourceJdbcTemplate") private val source: NamedParameterJdbcTemplate,
    @Qualifier(value = "destinationJdbcTemplate") private val destination: NamedParameterJdbcTemplate
) /*:
    CommandLineRunner*/
{

    private final val logger = LoggerFactory.getLogger(RomsToLomsMigration::class.java)
    private final val prefsRowMapper = SFM.newInstance().newRowMapper(MPref::class.java)
    private final val prefsParameterSourceFactory = SFM.newInstance().newSqlParameterSourceFactory(MPref::class.java)

    private final val grpLocParamsFactory = SFM.newInstance().newSqlParameterSourceFactory(MLocation::class.java)
    private final val locationsRowMapper = SFM.newInstance().newRowMapper(MLocation::class.java)

    fun run(vararg args: String?) {
        copyPrefs()
        copyItems()

        val prefectures = loadPrefectures()
        val items = loadItems()
        copyGroupLocations(prefectures)
        copyPickupLocations(prefectures)
        copyShipperLocations(prefectures)
        copyDestinationAreaLocations(prefectures)

        val locations = loadLocations()
        copyGroups(locations)
        copyPickups(locations)

        val pickups = loadPickups()
        val groups = loadGroups()
        copyPickupGroups(pickups, groups)
        copyShippers(locations)
        copyDestinationCompanies()

        val companies = loadCompanies()
        copyDestinationAreas(locations, companies)

        val areas = loadAreas()
        copyDestinationSpots(areas, items)

    }


    private fun copyPrefs() {
        val selectSql = """
            select p_value name, p_sort sort, cast(p_id as int) roms_id, 
            case when delete_flg = true then current_timestamp else null end deleted_at
            from m_params  where p_cat = 'pref' order by cast(p_id as int);
        """.trimIndent()

        val insertSql =
            "insert into m_pref (name, display_order, roms_id, deleted_at) values (:name, :sort, :roms_id, :deleted_at)"

        val prefs = source.query(selectSql, prefsRowMapper)
        destination.batchUpdate(insertSql, prefsParameterSourceFactory.newSqlParameterSources(prefs))
        logger.info("M_PREF migrated with {} rows", prefs.size)
    }

    private fun copyItems() {
        val selectSql = """
            select
                nm name, snm1, snm2, category, rolly1_flg, rolly2_flg, rolly3_flg, bcolor,
                fcolor, sort, opt2, opt3, poison_flg, cast(item_cd as integer) roms_id,
                case when delete_flg = true then current_timestamp else null end deleted_at
            from m_items;
        """.trimIndent()

        val insertSql = """
            insert into m_items (
                name, snm1, snm2, category, rolly1_flg, rolly2_flg, rolly3_flg, 
                bcolor, fcolor, sort, opt2, opt3, poison_flg, roms_id, deleted_at
            ) values (
                :name, :snm1, :snm2, :category, :rolly1Flg, :rolly2Flg, :rolly3Flg, 
                :bcolor, :fcolor, :sort, :opt2, :opt3, :poisonFlg, :romsId, :deletedAt
            )
        """.trimIndent()

        val items = source.query(selectSql, SFM.newInstance().newRowMapper(MItem::class.java))
        val batch = SFM.newInstance().newSqlParameterSourceFactory(MItem::class.java).newSqlParameterSources(items)
        destination.batchUpdate(insertSql, batch)
        logger.info("M_ITEM migrated with {} rows", items.size)
    }

    private fun copyGroupLocations(prefectures: Map<Int, Int>) {
        val selectSql = """
                select
                concat(lat, ', ', lng) lat_lng, zip, pref_cd pref_id, city_cd, add1, add1_kana, add2, add2_kana,
                add3, add3_kana, tel, fax, concat('m_group_', g_no) as unique_id,
                case when delete_flg = true then current_timestamp else null end deleted_at
            from m_group
            order by g_no;
            """.trimIndent()

        val groupLocations = source.query(selectSql, locationsRowMapper).map {
            it.prefId?.let { prefId ->
                it.copy(prefId = prefectures[prefId] ?: error("Prefecture not found for $prefId"))
            } ?: it
        }
        saveLocations(groupLocations)
    }

    private fun copyPickupLocations(prefectures: Map<Int, Int>) {
        val selectSql = """
                select
                ',' lat_lng, zip, pref_cd pref_id, city_cd, add1, add1_kana, add2, add2_kana,
                add3, add3_kana, tel, fax, concat('m_pickup_', cast(pickup_cd as integer)) as unique_id,
                case when delete_flg = true then current_timestamp else null end deleted_at
            from m_pickup
            order by cast(pickup_cd as integer);
            """.trimIndent()

        val pickupLocations = source.query(selectSql, locationsRowMapper).map {
            it.prefId?.let { prefId ->
                it.copy(prefId = prefectures[prefId] ?: error("Prefecture not found for $prefId"))
            } ?: it
        }
        saveLocations(pickupLocations)
    }

    private fun copyShipperLocations(prefectures: Map<Int, Int>) {
        val selectSql = """
                select
                ',' lat_lng, zip, pref_cd pref_id, city_cd, add1, add1_kana, add2, add2_kana,
                add3, add3_kana, tel, fax, concat('m_shipper_', cast(shipper_cd as integer)) as unique_id,
                case when delete_flg = true then current_timestamp else null end deleted_at
            from m_shipper
            order by cast(shipper_cd as integer);
            """.trimIndent()

        val shipperLocations = source.query(selectSql, locationsRowMapper).map {
            it.prefId?.let { prefId ->
                it.copy(prefId = prefectures[prefId] ?: error("Prefecture not found for $prefId"))
            } ?: it
        }
        saveLocations(shipperLocations)
    }

    private fun copyDestinationAreaLocations(prefectures: Map<Int, Int>) {
        val selectSql = """
                select
                ',' lat_lng, zip, pref_cd pref_id, city_cd, add1, add1_kana, add2, add2_kana,
                add3, add3_kana, tel, fax, concat('m_dest_area_', cast(da_no as integer)) as unique_id,
                case when delete_flg = true then current_timestamp else null end deleted_at
            from m_dest_area
            order by cast(da_no as integer);
            """.trimIndent()

        val destinationAreaLocations = source.query(selectSql, locationsRowMapper).map {
            it.prefId?.let { prefId ->
                it.copy(prefId = prefectures[prefId] ?: error("Prefecture not found for $prefId"))
            } ?: it
        }

        saveLocations(destinationAreaLocations)
    }

    private fun saveLocations(locations: List<MLocation>) {
        val insertSql = """
            insert into m_locations(
                lat_lng, zip, pref_id, city_cd, add1, add1_kana, add2, add2_kana, add3, add3_kana, 
                tel, fax, unique_id, deleted_at
            ) VALUES (
                :latLng, :zip, :prefId, :cityCd, :add1, :add1Kana, :add2, :add2Kana, :add3, :add3Kana, 
                :tel, :fax, :uniqueId, :deletedAt
            )
        """.trimIndent()
        destination.batchUpdate(insertSql, grpLocParamsFactory.newSqlParameterSources(locations))
        logger.info("M_LOCATION migrated with {} rows", locations.size)
    }

    private fun copyGroups(locations: Map<String, Int>) {
        val selectSql = """
            select nm, group_nm, group_kana, group_type, item_cat, concat('m_group_', g_no) location_id, opt1, opt2, g_no,
            case when delete_flg = true then current_timestamp else null end deleted_at
            from m_group
            order by g_no;
            """.trimIndent()
        val groups = source.query(selectSql, SFM.newInstance().newRowMapper(MGroup::class.java))
            .map { it.copy(locationId = locations[it.locationId].toString()) }

        val insertSql = """
                insert into m_group (nm, group_nm, group_kana, group_type, item_cat, location_id, opt1, opt2, roms_id, deleted_at)
                VALUES (:nm, :groupNm, :groupKana, :groupType, :itemCat, :locationId, :opt1, :opt2, :gNo, :deletedAt)
            """.trimIndent()
        val batch = SFM.newInstance().newSqlParameterSourceFactory(MGroup::class.java).newSqlParameterSources(groups)
        destination.batchUpdate(insertSql, batch)

        logger.info("M_GROUP migrated with {} rows", groups.size)
    }

    private fun copyPickups(locations: Map<String, Int>) {
        val selectSql = """
                select
                    nm, kana, snm1, snm2, concat('m_pickup_', cast(pickup_cd as integer)) location_id, 
                    start_time, lunch_time, end_time, opt1, opt2, opt3, cast(pickup_cd as integer) roms_id,
                    case when delete_flg = true then current_timestamp else null end deleted_at
                from m_pickup order by roms_id;
            """.trimIndent()
        val insertSql = """
                insert into m_pickups(
                    nm, kana, snm1, snm2, location_id, start_time, lunch_time, end_time, 
                    opt1, opt2, opt3, roms_id, deleted_at
                ) VALUES (
                    :nm, :kana, :snm1, :snm2, :locationId, :startTime, :lunchTime, :endTime, 
                    :opt1, :opt2, :opt3, :romsId, :deletedAt
                )
            """.trimIndent()

        val pickups = source.query(selectSql, SFM.newInstance().newRowMapper(MPickup::class.java))
            .map { it.copy(locationId = locations[it.locationId].toString()) }

        val batch = SFM.newInstance().newSqlParameterSourceFactory(MPickup::class.java).newSqlParameterSources(pickups)
        destination.batchUpdate(insertSql, batch)

        logger.info("M_PICKUP migrated with {} rows", pickups.size)
    }

    private fun copyPickupGroups(pickups: Map<Int, Int>, groups: Map<Int, Int>) {
        val sql = "select cast(pickup_cd as integer) pickup_id, g_no group_id from m_pickup_group order by pickup_id;"
        val insertSql = "insert into m_pickup_group (pickup_id, group_id) VALUES (:pickupId, :groupId)"

        val pickupGroups = source.query(sql, SFM.newInstance().newRowMapper(MPickupGroup::class.java)).map {
            it.copy(
                pickupId = pickups[it.pickupId] ?: error("Pickup not found for ${it.pickupId}"),
                groupId = groups[it.groupId] ?: error("Group not found for ${it.groupId}")
            )
        }

        val batch = SFM.newInstance().newSqlParameterSourceFactory(MPickupGroup::class.java)
            .newSqlParameterSources(pickupGroups)
        destination.batchUpdate(insertSql, batch)

        logger.info("M_PICKUP_GROUP migrated with {} rows", pickupGroups.size)
    }

    private fun copyShippers(locations: Map<String, Int>) {
        val selectSql = """
                select charge_cd, nm, kana, snm1, snm2, concat('m_shipper_', cast(shipper_cd as integer)) location_id, 
                opt1, opt2, opt3, opt4, opt5, cast(shipper_cd as integer) roms_id, 
                case when delete_flg = true then current_timestamp else null end deleted_at
                from m_shipper order by roms_id;
            """.trimIndent()

        val insertSql = """
            insert into m_shipper (
                charge_cd, nm, kana, snm1, snm2, location_id, opt1, opt2, opt3, opt4, opt5, roms_id, deleted_at
            ) VALUES (
                :chargeCd, :nm, :kana, :snm1, :snm2, :locationId, :opt1, :opt2, :opt3, :opt4, :opt5, :romsId, :deletedAt
            )
        """.trimIndent()

        val data = source.query(selectSql, SFM.newInstance().newRowMapper(Shipper::class.java))
            .map { it.copy(locationId = locations[it.locationId].toString()) }
        val batch = SFM.newInstance().newSqlParameterSourceFactory(Shipper::class.java).newSqlParameterSources(data)
        destination.batchUpdate(insertSql, batch)

        logger.info("M_SHIPPER migrated with {} rows", data.size)
    }

    private fun copyDestinationCompanies() {
        val selectSql = """
                select
                    nm name, kana, opt1, opt2, opt3, cast(dc_no as integer) roms_id,
                    case when delete_flg = true then current_timestamp else null end deleted_at
                from m_dest_company order by roms_id;
            """.trimIndent()

        val insertSql = """
                insert into m_dest_company (name, kana, opt1, opt2, opt3, roms_id, deleted_at)
                VALUES (:name, :kana, :opt1, :opt2, :opt3, :romsId, :deletedAt)
            """.trimIndent()

        val destinationCompanies = source.query(selectSql, SFM.newInstance().newRowMapper(MDestCompany::class.java))
        val batch = SFM.newInstance().newSqlParameterSourceFactory(MDestCompany::class.java)
            .newSqlParameterSources(destinationCompanies)
        destination.batchUpdate(insertSql, batch)

        logger.info("M_DEST_COMPANY migrated with {} rows", destinationCompanies.size)
    }


    private fun copyDestinationAreas(locations: Map<String, Int>, companies: Map<Int, Int>) {
        val selectSql = """
            select
            dc_no dest_company_id, serial_no, nm, kana, concat('m_dest_area_', cast(da_no as integer)) location_id, 
            opt1, opt2, opt3, cast(da_no as integer) roms_id, 
            case when delete_flg = true then current_timestamp else null end deleted_at
        from m_dest_area order by roms_id;
        """.trimIndent()

        val insertSql = """
            insert into m_dest_area (
                dest_company_id, serial_no, nm, kana, opt1, opt2, opt3, 
                location_id, roms_id, deleted_at
            ) VALUES (
                :destCompanyId, :serialNo, :nm, :kana, :opt1, :opt2, :opt3, 
                :locationId, :romsId, :deletedAt
            )
        """.trimIndent()

        val destinationAreas = source.query(selectSql, SFM.newInstance().newRowMapper(MDestArea::class.java)).map {
            it.copy(locationId = locations[it.locationId].toString(), destCompanyId = companies[it.destCompanyId])
        }
        val batch = SFM.newInstance().newSqlParameterSourceFactory(MDestArea::class.java)
            .newSqlParameterSources(destinationAreas)
        destination.batchUpdate(insertSql, batch)

        logger.info("M_DEST_AREA migrated with {} rows", destinationAreas.size)
    }

    private fun copyDestinationSpots(areas: Map<Int, Int>, items: Map<Int, Int>) {
        val selectSql = """
                select
                    da_no areaNo, nm, spot, item_cd, kana1, kana2, snm1, snm2,
                    dept1, dept2, person1, person2, contact1, contact2, cmt1_flg, cmt1,
                    cmt2_flg, cmt2, cmt3_flg, cmt3, cmt4_flg, cmt4, cmt5_flg, cmt5,
                    cmt6_flg, cmt6, cmt7_flg, cmt7, cmt8_flg, cmt8, cmt9_flg, cmt9,
                    cmt10_flg, cmt10, file_id, file1, file2, remarks,
                    cast(ds_no as integer) roms_id,
                    case when delete_flg = true then current_timestamp else null end deleted_at
                from m_dest_spot order by roms_id;
            """.trimIndent()

        val insertSql = """
                insert into m_dest_spot (dest_area_id, serial_no, nm, kana, opt1, opt2, opt3, roms_id, deleted_at)
                VALUES (:destAreaId, :serialNo, :nm, :kana, :opt1, :opt2, :opt3, :romsId, :deletedAt)
            """.trimIndent()

        val destinationSpots = source.query(selectSql, SFM.newInstance().newRowMapper(MDestSpot::class.java)).map {
            it.copy(destAreaId = areas[it.destAreaId] ?: error("Area not found for ${it.destAreaId}"))
        }

        val batch = SFM.newInstance().newSqlParameterSourceFactory(MDestSpot::class.java)
            .newSqlParameterSources(destinationSpots)
        destination.batchUpdate(insertSql, batch)

        logger.info("M_DEST_SPOT migrated with {} rows", destinationSpots.size)
    }


    private fun loadPrefectures() = destination.query("select id, roms_id from m_pref", toPair).toMap()

    private fun loadItems() = destination.query("select id, roms_id from m_items", toPair).toMap()

    private fun loadGroups() = destination.query("select id, roms_id from m_group", toPair).toMap()

    private fun loadCompanies() = destination.query("select id, roms_id from m_dest_company", toPair).toMap()

    private fun loadPickups() = destination.query("select id, roms_id from m_pickups", toPair).toMap()

    private fun loadAreas() = destination.query("select id, roms_id from m_dest_area", toPair).toMap()

    private fun loadLocations() = destination.query("select id, unique_id roms_id from m_locations") { rs, _ ->
        rs.getString("roms_id") to rs.getInt("id")
    }.toMap()

    val toPair = RowMapper { rs: ResultSet, _: Int -> rs.getInt("roms_id") to rs.getInt("id") }

}

