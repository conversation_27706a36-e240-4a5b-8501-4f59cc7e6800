package com.sd.loms.migration

import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate
import org.simpleflatmapper.jdbc.spring.JdbcTemplateMapperFactory as SFM

class DestinationCompaniesMigration(
    private val source: NamedParameterJdbcTemplate,
    private val destination: NamedParameterJdbcTemplate
) : TableMigration {

    override fun migrate(): Any {
        val selectSql = """
                select
                    nm name, kana, opt1, opt2, opt3, cast(dc_no as integer) roms_id,
                    case when delete_flg = true then current_timestamp else null end deleted_at
                from m_dest_company order by roms_id;
            """.trimIndent()

        val insertSql = """
                insert into m_dest_company (name, kana, opt1, opt2, opt3, roms_id, deleted_at)
                VALUES (:name, :kana, :opt1, :opt2, :opt3, :romsId, :deletedAt)
            """.trimIndent()

        val destinationCompanies = source.query(selectSql, SFM.newInstance().newRowMapper(MDestCompany::class.java))
        val batch = SFM.newInstance().newSqlParameterSourceFactory(MDestCompany::class.java)
            .newSqlParameterSources(destinationCompanies)
        destination.batchUpdate(insertSql, batch)
        return destinationCompanies
    }


}