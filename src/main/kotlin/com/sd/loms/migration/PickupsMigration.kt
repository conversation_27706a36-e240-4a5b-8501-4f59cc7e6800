package com.sd.loms.migration


import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate
import org.simpleflatmapper.jdbc.spring.JdbcTemplateMapperFactory as SFM

class PickupsMigration(
    private val source: NamedParameterJdbcTemplate,
    private val destination: NamedParameterJdbcTemplate,
    private val locations: Map<String, Int>
) : TableMigration {

    override fun migrate(): Any {
        val selectSql = """
            select
                nm, kana, snm1, snm2, concat('m_pickup_', cast(pickup_cd as integer)) location_id,
                start_time, lunch_time, end_time, opt1, opt2, opt3, cast(pickup_cd as integer) roms_id,
                case when delete_flg = true then current_timestamp else null end deleted_at
            from m_pickup order by roms_id;
        """.trimIndent()
        val insertSql = """
            insert into m_pickups(
                nm, kana, snm1, snm2, location_id, start_time, lunch_time, end_time,
                opt1, opt2, opt3, roms_id, deleted_at
            ) VALUES (
                :nm, :kana, :snm1, :snm2, :locationId, :startTime, :lunchTime, :endTime,
                :opt1, :opt2, :opt3, :romsId, :deletedAt
            )
        """.trimIndent()

        val pickups = source.query(selectSql, SFM.newInstance().newRowMapper(MPickup::class.java))
            .map { it.copy(locationId = locations[it.locationId].toString()) }

        val batch = SFM.newInstance().newSqlParameterSourceFactory(MPickup::class.java).newSqlParameterSources(pickups)
        destination.batchUpdate(insertSql, batch)
        return pickups
    }
}
