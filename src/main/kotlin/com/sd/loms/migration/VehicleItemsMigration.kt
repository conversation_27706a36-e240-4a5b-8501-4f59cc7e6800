package com.sd.loms.migration

import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate
import org.simpleflatmapper.jdbc.spring.JdbcTemplateMapperFactory as SFM
import com.sd.loms.migration.VehicleMaterialCategory as VehicleItem

class VehicleItemsMigration(
    private val destination: NamedParameterJdbcTemplate,
    private val vehicles: Map<Int, Int>,
    private val items: Map<Int, Int>,
) : TableMigration {

    override fun migrate(): Any {
        val vehicleItems = mutableListOf<VehicleItem>()

        fun addVehicleItems(vehicleId: Int, itemIds: List<String>) {
            val vehicle = vehicles[vehicleId] ?: error("Vehicle not found for $vehicleId")
            itemIds.forEach {
                vehicleItems.add(VehicleItem(vehicle, items[it.toInt()] ?: error("Item missing: $it")))
            }
        }

        fun addVehiclesItems(vehicleIds: List<Int>, itemIds: List<String>) {
            vehicleIds.forEach { vehicleId ->
                addVehicleItems(vehicleId, itemIds)
            }
        }

        val v1Items = listOf(
            "00001", "00002", "00004", "00005", "00007", "00008", "00015", "00020", "00021", "00024", "00025", "00060",
            "00110", "10013", "10015", "20429", "60474", "60475", "60477", "60478", "60481", "60483", "60485"
        )
        addVehicleItems(1, v1Items)

        val v17a20SecItems = listOf("00035", "00058", "60486")
        addVehiclesItems(listOf(17, 20), listOf("00010", "00011", "00067", "60465"))
        addVehiclesItems(listOf(17, 20), v17a20SecItems)

        val v160Items = listOf(
            "00001", "00002", "00004", "00005", "00007", "00008", "00015", "00020", "00021", "00024", "00025", "00060",
            "00067", "00110", "10009", "10013", "10015", "20429", "20441", "20442", "60463", "60464", "60465", "60474",
            "60475", "60477", "60478", "60481", "60483", "60485"
        )
        val v160SecItems = listOf("00018", "00035", "00058", "00401", "20369", "20398", "20403", "20428", "60467")
        addVehicleItems(160, v160Items)
        addVehicleItems(160, v160SecItems)

        val v164Items = listOf(
            "00001", "00002", "00004", "00005", "00007", "00008", "00015", "00020", "00021", "00024",
            "00025", "00110", "10015", "20429", "60464"
        )
        addVehicleItems(164, v164Items)

        val v180SecItems = listOf("60465")
        addVehicleItems(180, listOf("00010", "00011", "00067", "60486"))
        addVehicleItems(180, v180SecItems)

        val vehicles186a223a228 = listOf(
            186, 223, 228, 239, 273, 279, 287, 304, 307, 315, 326, 330,
            336, 341, 344, 359, 363, 364, 366, 370, 371, 372, 375, 380
        )
        val v186a223a228Items = listOf(
            "00001", "00002", "00004", "00005", "00007", "00008", "00015", "00020", "00021", "00024", "00025", "00060",
            "00110", "10009", "10013", "10015", "20429", "20441", "60463", "60464", "60474", "60475", "60477", "60478",
            "60481", "60483", "60485"
        )
        val v186a223a228SItems = listOf("00018", "00019", "00401", "20369", "20398", "20403", "20428", "20442", "60467")
        addVehiclesItems(vehicles186a223a228, v186a223a228Items)
        addVehiclesItems(vehicles186a223a228, v186a223a228SItems)
        addVehicleItems(337, v186a223a228Items)

        // CHECK THIS
        val v337SecItems = listOf("00018", "00035", "00058", "00401", "20369", "20398", "20403", "20428", "20442", "60467")
        addVehicleItems(337, v337SecItems)

        val v230a250Items = listOf("00010", "00011", "00067", "60465", "60486")
        val vehicles230a250 = listOf(230, 250, 261, 318)
        val v230a250a365SecItems = listOf("00035")
        addVehiclesItems(vehicles230a250, v230a250Items)
        addVehiclesItems(vehicles230a250, v230a250a365SecItems)

        // CHECK THIS
        addVehicleItems(365, listOf("00010", "00011", "60465", "60486"))
        addVehicleItems(365, v230a250a365SecItems)


        val v288Items = listOf("00001", "00002", "00004", "00005", "00007", "00008", "00015")
        val v288SecItems = listOf("00018", "00019")
        addVehiclesItems(listOf(288), v288Items)
        addVehiclesItems(listOf(288), v288SecItems)

        val batch = SFM.newInstance().newSqlParameterSourceFactory(VehicleItem::class.java)
            .newSqlParameterSources(vehicleItems)

        val insertSql = """
                insert into vehicle_material_categories (vehicle_id, item_id) 
                values (:vehicleId, :itemId)
        """.trimMargin()
        val result = destination.batchUpdate(insertSql, batch)


        val cleaningRequiredItems = v17a20SecItems + v160SecItems + v180SecItems + v186a223a228SItems + v337SecItems + v230a250a365SecItems + v288SecItems
        val updateStatement = "update m_items set cleaning_flg = true where roms_id in (:romsIds)"
        val distinctItems = cleaningRequiredItems.distinct()
        destination.update(updateStatement, mapOf("romsIds" to distinctItems))
        return result.size
    }
}
