package com.sd.loms.migration

import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate
import org.simpleflatmapper.jdbc.spring.JdbcTemplateMapperFactory as SFM

class VehiclesMigration(
    private val source: NamedParameterJdbcTemplate,
    private val destination: NamedParameterJdbcTemplate,
    private val groups: Map<Int, Int>
) : TableMigration {

    override fun migrate(): Any {
        val selectSql = """
            select g_no group_id, serial_nm, number, number_plate, belong_flg, vehicle_type, tank_type, tank_limit1, 
                   tank_limit2, total_limit, nm as name, model, initial_ym, inspect_ym, remarks, tank_size1, tank_size2, gas, 
                   sort, cast(vehicle_cd as integer) roms_id, case when delete_flg = true then current_timestamp else null end deleted_at
            from m_vehicles order by cast(vehicle_cd as integer);
        """.trimIndent()

        val insertSql = """
            insert into m_vehicles (
                group_id, serial_nm, number, number_plate, belong_flg, vehicle_type, tank_type, 
                tank_limit1, tank_limit2, total_limit, name, model, initial_ym, inspect_ym, 
                remarks, tank_size1, tank_size2, gas, display_order, roms_id, deleted_at
            ) values (
                :groupId, :serialNm, :number, :numberPlate, :belongFlg, :vehicleType, :tankType, 
                :tankLimit1, :tankLimit2, :totalLimit, :name, :model, :initialYm, :inspectYm, 
                :remarks, :tankSize1, :tankSize2, :gas, :sort, :romsId, :deletedAt
            )
        """.trimIndent()

        val items = source.query(selectSql, SFM.newInstance().newRowMapper(MVehicle::class.java)).map {
            it.copy(
                groupId = groups[it.groupId] ?: error("Group not found for ${it.groupId}")
            )
        }
        val batch = SFM.newInstance().newSqlParameterSourceFactory(MVehicle::class.java).newSqlParameterSources(items)
        destination.batchUpdate(insertSql, batch)
        return items
    }
}
