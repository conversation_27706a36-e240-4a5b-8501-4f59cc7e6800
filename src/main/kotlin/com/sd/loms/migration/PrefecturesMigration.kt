package com.sd.loms.migration

import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate
import org.simpleflatmapper.jdbc.spring.JdbcTemplateMapperFactory as SFM

class PrefecturesMigration(
    private val source: NamedParameterJdbcTemplate,
    private val destination: NamedParameterJdbcTemplate
) : TableMigration {
    private val prefsRowMapper = SFM.newInstance().newRowMapper(MPref::class.java)
    private val prefsParameterSourceFactory = SFM.newInstance().newSqlParameterSourceFactory(MPref::class.java)

    val selectSql = """
            select p_value name, p_sort sort, cast(p_id as int) roms_id,
            case when delete_flg = true then current_timestamp else null end deleted_at
            from m_params where p_cat = 'pref' order by cast(p_id as int);
        """.trimIndent()

    val insertSql =
        "insert into m_pref (name, display_order, roms_id, deleted_at) values (:name, :sort, :roms_id, :deleted_at)"


    override fun migrate(): Any {
        val prefs = source.query(selectSql, prefsRowMapper)
        destination.batchUpdate(insertSql, prefsParameterSourceFactory.newSqlParameterSources(prefs))
        return prefs
    }
}
