package com.sd.loms.migration


import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate
import org.simpleflatmapper.jdbc.spring.JdbcTemplateMapperFactory as SFM

class GroupMigration(
    private val source: NamedParameterJdbcTemplate,
    private val destination: NamedParameterJdbcTemplate,
    private val locations: Map<String, Int>
) : TableMigration {

    override fun migrate(): Any {
        val selectSql = """
            select nm, group_nm, group_kana, group_type, item_cat, concat('m_group_', g_no) location_id, opt1, opt2, g_no,
            case when delete_flg = true then current_timestamp else null end deleted_at
            from m_group
            order by g_no;
        """.trimIndent()
        val groups = source.query(selectSql, SFM.newInstance().newRowMapper(MGroup::class.java))
            .map { it.copy(locationId = locations[it.locationId].toString()) }

        val insertSql = """
            insert into m_group (nm, group_nm, group_kana, group_type, item_cat, location_id, opt1, opt2, roms_id, deleted_at)
            VALUES (:nm, :groupNm, :groupKana, :groupType, :itemCat, :locationId, :opt1, :opt2, :gNo, :deletedAt)
        """.trimIndent()
        val batch = SFM.newInstance().newSqlParameterSourceFactory(MGroup::class.java).newSqlParameterSources(groups)
        destination.batchUpdate(insertSql, batch)
        return groups
    }
}
