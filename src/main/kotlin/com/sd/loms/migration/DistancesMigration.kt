package com.sd.loms.migration

import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate
import org.simpleflatmapper.jdbc.spring.JdbcTemplateMapperFactory as SFM

class DistancesMigration(
    private val google: NamedParameterJdbcTemplate,
    private val destination: NamedParameterJdbcTemplate,
    private val locations: Map<String, Int>,
) : TableMigration {
    override fun migrate(): Any {
        val distancesSelectSql = """
            select (select unique_id from m_locations l where l.id = from_loc_id) as from_unique_id,
                   (select unique_id from m_locations l where l.id = to_loc_id) as to_unique_id,
                   duration
            from m_distances;
        """.trimIndent()

        val insertSql =
            "insert into m_distances(from_loc_id, to_loc_id, duration) VALUES (:fromLocId, :toLocId, :duration)"

        val distances = google.query(distancesSelectSql) { rs, _ ->
            MDistance(
                rs.getString("from_unique_id"),
                rs.getString("to_unique_id"),
                rs.getInt("duration")
            )
        }.map {
            it.copy(
                fromLocId = locations[it.fromLocId]?.toString() ?: error("Location not found for ${it.fromLocId}"),
                toLocId = locations[it.toLocId]?.toString() ?: error("Location not found for ${it.toLocId}")
            )
        }


        val distancesParamsFactory = SFM.newInstance().newSqlParameterSourceFactory(MDistance::class.java)
        destination.batchUpdate(insertSql, distancesParamsFactory.newSqlParameterSources(distances))
        return distances.size
    }

}