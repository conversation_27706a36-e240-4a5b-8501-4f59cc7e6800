package com.sd.loms.migration


import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate
import org.simpleflatmapper.jdbc.spring.JdbcTemplateMapperFactory as SFM

class ShipperLocationsMigration(
    private val source: NamedParameterJdbcTemplate,
    private val prefectures: Map<Int, Int>
) : TableMigration {

    private val locationsRowMapper = SFM.newInstance().newRowMapper(MLocation::class.java)


    override fun migrate(): Any {
        val selectSql = """
            select
                '-' lat_lng, zip, pref_cd pref_id, city_cd, add1, add1_kana, add2, add2_kana,
                add3, add3_kana, tel, fax, concat('m_shipper_', cast(shipper_cd as integer)) as unique_id,
                case when delete_flg = true then current_timestamp else null end deleted_at
            from m_shipper
            order by cast(shipper_cd as integer);
        """.trimIndent()

        val shipperLocations = source.query(selectSql, locationsRowMapper).map {
            it.prefId?.let { prefId ->
                it.copy(prefId = prefectures[prefId] ?: error("Prefecture not found for $prefId"))
            } ?: it
        }
        return shipperLocations
    }


}