package com.sd.loms.migration

import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate
import org.simpleflatmapper.jdbc.spring.JdbcTemplateMapperFactory as SFM

class SalaryMigration(
    private val source: NamedParameterJdbcTemplate,
    private val destination: NamedParameterJdbcTemplate
) : TableMigration {

    override fun migrate(): Any {
        val selectSql = """
            select class as cls, rank, base, g_1, g_2, g_3, g_4, g_5, salary_cd roms_id, 
            case when delete_flg = true then current_timestamp else null end deleted_at
            from m_salary;
        """.trimIndent()

        val insertSql = """
            insert into m_salary(
                class, rank, base, g_1, g_2, g_3, g_4, g_5, roms_id, deleted_at
            ) values (
                :cls, :rank, :base, :g1, :g2, :g3, :g4, :g5, :romsId, :deletedAt
            )
        """.trimIndent()

        val items = source.query(selectSql, SFM.newInstance().newRowMapper(MSalary::class.java))
        val batch = SFM.newInstance().newSqlParameterSourceFactory(MSalary::class.java).newSqlParameterSources(items)
        destination.batchUpdate(insertSql, batch)
        return items
    }
}
