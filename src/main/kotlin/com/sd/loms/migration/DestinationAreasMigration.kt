package com.sd.loms.migration


import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate
import kotlin.collections.get
import kotlin.collections.map
import org.simpleflatmapper.jdbc.spring.JdbcTemplateMapperFactory as SFM

class DestinationAreasMigration(
    private val source: NamedParameterJdbcTemplate,
    private val destination: NamedParameterJdbcTemplate,
    private val locations: Map<String, Int>,
    private val companies: Map<Int, Int>
) : TableMigration {
    override fun migrate(): Any {

        val selectSql = """
            select
                dc_no dest_company_id, serial_no, nm, kana, concat('m_dest_area_', cast(da_no as integer)) location_id, 
                opt1, opt2, opt3, cast(da_no as integer) roms_id, 
                case when delete_flg = true then current_timestamp else null end deleted_at
            from m_dest_area order by roms_id;
        """.trimIndent()

        val insertSql = """
            insert into m_dest_area (
                dest_company_id, serial_no, nm, kana, opt1, opt2, opt3, 
                location_id, roms_id, deleted_at
            ) VALUES (
                :destCompanyId, :serialNo, :nm, :kana, :opt1, :opt2, :opt3, 
                :locationId, :romsId, :deletedAt
            )
        """.trimIndent()

        val destinationAreas = source.query(selectSql, SFM.newInstance().newRowMapper(MDestArea::class.java)).map {
            it.copy(locationId = locations[it.locationId].toString(), destCompanyId = companies[it.destCompanyId])
        }
        val batch = SFM.newInstance().newSqlParameterSourceFactory(MDestArea::class.java)
            .newSqlParameterSources(destinationAreas)
        destination.batchUpdate(insertSql, batch)
        return destinationAreas
    }

}