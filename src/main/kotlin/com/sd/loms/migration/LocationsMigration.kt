package com.sd.loms.migration

import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate
import org.simpleflatmapper.jdbc.spring.JdbcTemplateMapperFactory as SFM

class LocationsMigration(
    private val google: NamedParameterJdbcTemplate,
    private val destination: NamedParameterJdbcTemplate,
    private val locations: List<MLocation>,
) : TableMigration {
    override fun migrate(): Any {
        val googleCoordinatesSelectSql = "select unique_id, lat_lng from m_locations;"

        val googleCoordinates = google.query(googleCoordinatesSelectSql) { rs, _ ->
            rs.getString("unique_id") to rs.getString("lat_lng")
        }.toMap()


        val transformedLocations = locations.map {
            it.copy(
                latLng = googleCoordinates[it.uniqueId] ?: it.latLng,
                type = it.uniqueId.substringBeforeLast('_'),
                typeId = it.uniqueId.substringAfterLast('_').toLongOrNull()
            )
        }

        val insertSql = """
            insert into m_locations(
                lat_lng, zip, pref_id, city_cd, add1, add1_kana, add2, add2_kana, add3, add3_kana,
                tel, fax, unique_id, type, type_id, deleted_at
            ) VALUES (
                :latLng, :zip, :prefId, :cityCd, :add1, :add1Kana, :add2, :add2Kana, :add3, :add3Kana,
                :tel, :fax, :uniqueId, :type, :typeId, :deletedAt
            )
        """.trimIndent()
        val grpLocParamsFactory = SFM.newInstance().newSqlParameterSourceFactory(MLocation::class.java)
        destination.batchUpdate(insertSql, grpLocParamsFactory.newSqlParameterSources(transformedLocations))
        return transformedLocations
    }
}