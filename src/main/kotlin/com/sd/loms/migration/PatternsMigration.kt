package com.sd.loms.migration

import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate
import org.simpleflatmapper.jdbc.spring.JdbcTemplateMapperFactory as SFM

class PatternsMigration(
    private val destination: NamedParameterJdbcTemplate,
    private val pickups: Map<Int, Int>,
    private val deliveries: Map<Int, Int>,
    private val vehicles: Map<Int, Int>,
    private val shippers: Map<Int, Int>,
    private val drivers: Map<String, Int?>,
    private val items: Map<Int, Int>,
) : TableMigration {
    override fun migrate(): Any {


        val patterns = listOf(
            Pattern (vehicleId = "00318", shipperId = "00101", pickupId = "00004", destinationId = "0000002626", itemId = "00010", driver = "溝下"),
            Pattern (vehicleId = "00164", shipperId = "00101", pickupId = "00001", destinationId = "0000002177", itemId = "00001", driver = "槇本"),
            Pattern (vehicleId = "00344", shipperId = "00501", pickupId = "00016", destinationId = "0000004481", itemId = "00005", driver = "砂田"),
            Pattern (vehicleId = "00370", shipperId = "00501", pickupId = "00016", destinationId = "0000004343", itemId = "00001", driver = "濱崎"),
            Pattern (vehicleId = "00318", shipperId = "00101", pickupId = "00004", destinationId = "0000000542", itemId = "00010", driver = "溝下"),
            Pattern (vehicleId = "00164", shipperId = "60116", pickupId = "00136", destinationId = "0000004651", itemId = "00008", driver = "槇本"),
            Pattern (vehicleId = "00017", shipperId = "00101", pickupId = "00004", destinationId = "0000000274", itemId = "00010", driver = "仲"),
            Pattern (vehicleId = "00318", shipperId = "01201", pickupId = "00134", destinationId = "0000003100", itemId = "00010", driver = "溝下"),
            Pattern (vehicleId = "00344", shipperId = "00501", pickupId = "00016", destinationId = "0000004343", itemId = "00001", driver = "砂田"),
            Pattern (vehicleId = "00017", shipperId = "01201", pickupId = "00134", destinationId = "0000003100", itemId = "00010", driver = "仲"),
            Pattern (vehicleId = "00017", shipperId = "00101", pickupId = "00004", destinationId = "0000000542", itemId = "00010", driver = "仲"),
            Pattern (vehicleId = "00341", shipperId = "00401", pickupId = "00010", destinationId = "0000000299", itemId = "00001", driver = "俣野"),
            Pattern (vehicleId = "00288", shipperId = "00101", pickupId = "00004", destinationId = "0000004112", itemId = "00018", driver = "播松運輸"),
            Pattern (vehicleId = "00288", shipperId = "00101", pickupId = "00004", destinationId = "0000000163", itemId = "00018", driver = "播松運輸"),
            Pattern (vehicleId = "00261", shipperId = "01201", pickupId = "00134", destinationId = "0000003100", itemId = "00010", driver = "和久利"),
            Pattern (vehicleId = "00001", shipperId = "00101", pickupId = "00001", destinationId = "0000000077", itemId = "00002", driver = "久保"),
            Pattern (vehicleId = "00326", shipperId = "00101", pickupId = "00001", destinationId = "0000000219", itemId = "00005", driver = "田中孝治"),
            Pattern (vehicleId = "00164", shipperId = "03101", pickupId = "00119", destinationId = "0000002144", itemId = "00001", driver = "槇本"),
            Pattern (vehicleId = "00239", shipperId = "00401", pickupId = "00010", destinationId = "0000000299", itemId = "00001", driver = "髙田"),
            Pattern (vehicleId = "00315", shipperId = "00101", pickupId = "00001", destinationId = "0000002177", itemId = "00001", driver = "岡田"),
            Pattern (vehicleId = "00288", shipperId = "00101", pickupId = "00009", destinationId = "0000004111", itemId = "00019", driver = "播松運輸"),
            Pattern (vehicleId = "00365", shipperId = "01201", pickupId = "00134", destinationId = "0000003100", itemId = "00010", driver = "中山"),
            Pattern (vehicleId = "00344", shipperId = "00501", pickupId = "00016", destinationId = "0000004445", itemId = "00001", driver = "砂田"),
            Pattern (vehicleId = "00001", shipperId = "00101", pickupId = "00001", destinationId = "0000000636", itemId = "00001", driver = "久保"),
            Pattern (vehicleId = "00239", shipperId = "00101", pickupId = "00001", destinationId = "0000000219", itemId = "00005", driver = "髙田"),
            Pattern (vehicleId = "00318", shipperId = "04101", pickupId = "00134", destinationId = "0000004385", itemId = "00010", driver = "溝下"),
            Pattern (vehicleId = "00363", shipperId = "00401", pickupId = "00010", destinationId = "0000000299", itemId = "00001", driver = "武田"),
            Pattern (vehicleId = "00364", shipperId = "00401", pickupId = "00010", destinationId = "0000000299", itemId = "00001", driver = "宮本"),
            Pattern (vehicleId = "00164", shipperId = "60116", pickupId = "00256", destinationId = "0000004392", itemId = "00001", driver = "槇本"),
            Pattern (vehicleId = "00001", shipperId = "02301", pickupId = "50318", destinationId = "0000004496", itemId = "60477", driver = "久保"),
            Pattern (vehicleId = "00341", shipperId = "00101", pickupId = "00001", destinationId = "0000000219", itemId = "00005", driver = "俣野"),
            Pattern (vehicleId = "00001", shipperId = "00101", pickupId = "00001", destinationId = "0000000649", itemId = "00001", driver = "久保"),
            Pattern (vehicleId = "00261", shipperId = "00101", pickupId = "00004", destinationId = "0000000274", itemId = "00010", driver = "和久利"),
            Pattern (vehicleId = "00326", shipperId = "00401", pickupId = "00010", destinationId = "0000000299", itemId = "00001", driver = "田中孝治"),
            Pattern (vehicleId = "00366", shipperId = "00401", pickupId = "00010", destinationId = "0000000299", itemId = "00001", driver = "石岡"),
            Pattern (vehicleId = "00370", shipperId = "00501", pickupId = "00016", destinationId = "0000004481", itemId = "00005", driver = "濱崎"),
            Pattern (vehicleId = "00017", shipperId = "02801", pickupId = "00005", destinationId = "0000003118", itemId = "00010", driver = "仲"),
            Pattern (vehicleId = "00366", shipperId = "00101", pickupId = "00001", destinationId = "0000000219", itemId = "00005", driver = "石岡"),
            Pattern (vehicleId = "00318", shipperId = "00101", pickupId = "00004", destinationId = "0000004167", itemId = "00010", driver = "溝下"),
            Pattern (vehicleId = "00318", shipperId = "60114", pickupId = "00134", destinationId = "0000004618", itemId = "00010", driver = "溝下"),
            Pattern (vehicleId = "00315", shipperId = "00101", pickupId = "00001", destinationId = "0000000541", itemId = "00001", driver = "岡田"),
            Pattern (vehicleId = "00370", shipperId = "00501", pickupId = "00016", destinationId = "0000004445", itemId = "00001", driver = "濱崎"),
            Pattern (vehicleId = "00363", shipperId = "00101", pickupId = "00001", destinationId = "0000000219", itemId = "00005", driver = "武田"),
            Pattern (vehicleId = "00001", shipperId = "00101", pickupId = "00001", destinationId = "0000000523", itemId = "00005", driver = "久保"),
            Pattern (vehicleId = "00180", shipperId = "02902", pickupId = "00136", destinationId = "0000004304", itemId = "00010", driver = "中山"),
            Pattern (vehicleId = "00001", shipperId = "00101", pickupId = "00001", destinationId = "0000000031", itemId = "00005", driver = "久保"),
            Pattern (vehicleId = "00288", shipperId = "00101", pickupId = "00009", destinationId = "0000000317", itemId = "00019", driver = "播松運輸"),
            Pattern (vehicleId = "00164", shipperId = "03101", pickupId = "00119", destinationId = "0000002133", itemId = "00001", driver = "槇本"),
            Pattern (vehicleId = "00180", shipperId = "02902", pickupId = "00136", destinationId = "0000004304", itemId = "00010", driver = "古川"),
            Pattern (vehicleId = "00228", shipperId = "00501", pickupId = "00016", destinationId = "0000004343", itemId = "00001", driver = "砂田"),
            Pattern (vehicleId = "00318", shipperId = "03501", pickupId = "00004", destinationId = "0000003519", itemId = "00010", driver = "溝下"),
            Pattern (vehicleId = "00261", shipperId = "02801", pickupId = "00005", destinationId = "0000003118", itemId = "00010", driver = "和久利"),
            Pattern (vehicleId = "00261", shipperId = "00101", pickupId = "00004", destinationId = "0000000542", itemId = "00010", driver = "和久利"),
            Pattern (vehicleId = "00017", shipperId = "60116", pickupId = "00256", destinationId = "0000004607", itemId = "00010", driver = "仲"),
            Pattern (vehicleId = "00017", shipperId = "00301", pickupId = "00001", destinationId = "0000002522", itemId = "00010", driver = "仲"),
            Pattern (vehicleId = "00337", shipperId = "03101", pickupId = "00119", destinationId = "0000002144", itemId = "00001", driver = "山本晋平"),
            Pattern (vehicleId = "00372", shipperId = "03101", pickupId = "00119", destinationId = "0000002144", itemId = "00001", driver = "渡邊"),
            Pattern (vehicleId = "00365", shipperId = "02902", pickupId = "00136", destinationId = "0000004304", itemId = "00010", driver = "中山"),
            Pattern (vehicleId = "00001", shipperId = "03101", pickupId = "00119", destinationId = "0000002711", itemId = "00001", driver = "久保"),
            Pattern (vehicleId = "00164", shipperId = "02001", pickupId = "00119", destinationId = "0000004394", itemId = "00001", driver = "槇本"),
            Pattern (vehicleId = "00001", shipperId = "00101", pickupId = "00001", destinationId = "0000000030", itemId = "00005", driver = "久保"),
            Pattern (vehicleId = "00001", shipperId = "00101", pickupId = "00001", destinationId = "0000000117", itemId = "00005", driver = "久保"),
            Pattern (vehicleId = "00273", shipperId = "00101", pickupId = "00001", destinationId = "0000000219", itemId = "00005", driver = "佐々木"),
            Pattern (vehicleId = "00250", shipperId = "01201", pickupId = "00134", destinationId = "0000003100", itemId = "00010", driver = "杉本"),
            Pattern (vehicleId = "00164", shipperId = "60116", pickupId = "00136", destinationId = "0000004175", itemId = "00005", driver = "槇本"),
            Pattern (vehicleId = "00279", shipperId = "00101", pickupId = "00001", destinationId = "0000000219", itemId = "00005", driver = "桑原"),
            Pattern (vehicleId = "00359", shipperId = "03101", pickupId = "00119", destinationId = "0000002144", itemId = "00001", driver = "松元"),
            Pattern (vehicleId = "00230", shipperId = "01201", pickupId = "00134", destinationId = "0000003100", itemId = "00010", driver = "古川"),
            Pattern (vehicleId = "00363", shipperId = "03101", pickupId = "00119", destinationId = "0000002144", itemId = "00001", driver = "武田"),
            Pattern (vehicleId = "00261", shipperId = "04101", pickupId = "00134", destinationId = "0000004385", itemId = "00010", driver = "和久利"),
            Pattern (vehicleId = "00304", shipperId = "00501", pickupId = "00016", destinationId = "0000004343", itemId = "00001", driver = "濱崎"),
            Pattern (vehicleId = "00364", shipperId = "00501", pickupId = "00102", destinationId = "0000004192", itemId = "00001", driver = "宮本"),
            Pattern (vehicleId = "00366", shipperId = "00501", pickupId = "00102", destinationId = "0000004597", itemId = "00001", driver = "石岡"),
            Pattern (vehicleId = "00273", shipperId = "03101", pickupId = "00119", destinationId = "0000002144", itemId = "00001", driver = "佐々木"),
            Pattern (vehicleId = "00364", shipperId = "00101", pickupId = "00001", destinationId = "0000000219", itemId = "00005", driver = "宮本"),
            Pattern (vehicleId = "00261", shipperId = "00301", pickupId = "00001", destinationId = "0000002522", itemId = "00010", driver = "和久利"),
            Pattern (vehicleId = "00359", shipperId = "00101", pickupId = "00001", destinationId = "0000004196", itemId = "00001", driver = "松元"),
            Pattern (vehicleId = "00239", shipperId = "00501", pickupId = "00102", destinationId = "0000004192", itemId = "00001", driver = "髙田"),
            Pattern (vehicleId = "00228", shipperId = "00501", pickupId = "00016", destinationId = "0000004343", itemId = "00001", driver = "濱崎"),
            Pattern (vehicleId = "00337", shipperId = "00501", pickupId = "00102", destinationId = "0000004192", itemId = "00001", driver = "山本晋平"),
            Pattern (vehicleId = "00288", shipperId = "00101", pickupId = "00009", destinationId = "0000002764", itemId = "00019", driver = "播松運輸"),
            Pattern (vehicleId = "00164", shipperId = "03101", pickupId = "00119", destinationId = "0000002651", itemId = "00001", driver = "槇本"),
            Pattern (vehicleId = "00288", shipperId = "00101", pickupId = "00009", destinationId = "0000000075", itemId = "00019", driver = "播松運輸"),
            Pattern (vehicleId = "00164", shipperId = "00501", pickupId = "00102", destinationId = "0000002183", itemId = "00001", driver = "槇本"),
            Pattern (vehicleId = "00363", shipperId = "03501", pickupId = "00004", destinationId = "0000003915", itemId = "00001", driver = "武田"),
            Pattern (vehicleId = "00365", shipperId = "00101", pickupId = "00004", destinationId = "0000000542", itemId = "00010", driver = "中山"),
            Pattern (vehicleId = "00279", shipperId = "02801", pickupId = "00009", destinationId = "0000003908", itemId = "00001", driver = "桑原"),
            Pattern (vehicleId = "00250", shipperId = "00101", pickupId = "00004", destinationId = "0000000542", itemId = "00010", driver = "下木原"),
            Pattern (vehicleId = "00366", shipperId = "02801", pickupId = "00009", destinationId = "0000003908", itemId = "00001", driver = "石岡"),
            Pattern (vehicleId = "00315", shipperId = "03101", pickupId = "00119", destinationId = "0000002144", itemId = "00001", driver = "岡田"),
            Pattern (vehicleId = "00307", shipperId = "00101", pickupId = "00004", destinationId = "0000004112", itemId = "00018", driver = "山下"),
            Pattern (vehicleId = "00228", shipperId = "00501", pickupId = "00016", destinationId = "0000004343", itemId = "00001", driver = "面谷"),
            Pattern (vehicleId = "00273", shipperId = "00501", pickupId = "00102", destinationId = "0000004192", itemId = "00001", driver = "佐々木"),
            Pattern (vehicleId = "00164", shipperId = "03101", pickupId = "00119", destinationId = "0000004642", itemId = "00001", driver = "槇本"),
            Pattern (vehicleId = "00370", shipperId = "03101", pickupId = "00119", destinationId = "0000004253", itemId = "00001", driver = "濱崎"),
            Pattern (vehicleId = "00315", shipperId = "03101", pickupId = "00119", destinationId = "0000004253", itemId = "00001", driver = "岡田"),
            Pattern (vehicleId = "00359", shipperId = "00101", pickupId = "00001", destinationId = "0000002177", itemId = "00001", driver = "松元"),
            Pattern (vehicleId = "00318", shipperId = "01201", pickupId = "00134", destinationId = "0000004206", itemId = "00010", driver = "溝下"),
            Pattern (vehicleId = "00279", shipperId = "00101", pickupId = "00001", destinationId = "0000003875", itemId = "00001", driver = "桑原"),
            Pattern (vehicleId = "00359", shipperId = "00101", pickupId = "00001", destinationId = "0000000219", itemId = "00005", driver = "松元"),
            Pattern (vehicleId = "00372", shipperId = "00101", pickupId = "00001", destinationId = "0000000219", itemId = "00005", driver = "渡邊"),
            Pattern (vehicleId = "00372", shipperId = "03101", pickupId = "00119", destinationId = "0000004642", itemId = "00001", driver = "渡邊"),
            Pattern (vehicleId = "00315", shipperId = "00101", pickupId = "00001", destinationId = "0000004196", itemId = "00001", driver = "岡田"),
            Pattern (vehicleId = "00337", shipperId = "00101", pickupId = "00001", destinationId = "0000000219", itemId = "00005", driver = "山本晋平"),
            Pattern (vehicleId = "00366", shipperId = "00101", pickupId = "00001", destinationId = "0000004196", itemId = "00001", driver = "石岡"),
            Pattern (vehicleId = "00375", shipperId = "00501", pickupId = "00016", destinationId = "0000004343", itemId = "00001", driver = "白石"),
            Pattern (vehicleId = "00315", shipperId = "60116", pickupId = "00256", destinationId = "0000004392", itemId = "00001", driver = "岡田"),
            Pattern (vehicleId = "00307", shipperId = "00101", pickupId = "00001", destinationId = "0000002177", itemId = "00001", driver = "山下"),
            Pattern (vehicleId = "00273", shipperId = "00101", pickupId = "00001", destinationId = "0000002177", itemId = "00001", driver = "佐々木"),
            Pattern (vehicleId = "00017", shipperId = "60114", pickupId = "00134", destinationId = "0000004618", itemId = "00010", driver = "仲"),
            Pattern (vehicleId = "00326", shipperId = "02801", pickupId = "00009", destinationId = "0000003908", itemId = "00001", driver = "田中孝治"),
            Pattern (vehicleId = "00330", shipperId = "00101", pickupId = "00001", destinationId = "0000000219", itemId = "00005", driver = "近藤"),
            Pattern (vehicleId = "00288", shipperId = "00101", pickupId = "00004", destinationId = "0000002058", itemId = "00018", driver = "播松運輸"),
            Pattern (vehicleId = "00363", shipperId = "00501", pickupId = "00102", destinationId = "0000004192", itemId = "00001", driver = "武田"),
            Pattern (vehicleId = "00365", shipperId = "60114", pickupId = "00134", destinationId = "0000004618", itemId = "00010", driver = "中山"),
            Pattern (vehicleId = "00330", shipperId = "03101", pickupId = "00119", destinationId = "0000002144", itemId = "00001", driver ="近藤"),
            Pattern (vehicleId = "00372", shipperId = "03501", pickupId = "0004",  destinationId = "0000003915", itemId = "00001", driver = "渡邊")
        )
            .mapNotNull {
                val driver = drivers[it.driver]?.toString()

                if(driver == null) {
                    println("Driver not found: ${it.driver}")
                }

                if (driver != null) {
                    it.copy(
                        vehicleId = vehicles[it.vehicleId.toInt()]!!.toString(),
                        shipperId = shippers[it.shipperId.toInt()]!!.toString(),
                        pickupId = pickups[it.pickupId.toInt()]!!.toString(),
                        destinationId = deliveries[it.destinationId.toInt()]!!.toString(),
                        itemId = items[it.itemId.toInt()]!!.toString(),
                        driver = driver
                    )
                } else null
            }


        val insertSql = """
           INSERT INTO m_patterns (item_id, shipper_id, pickup_id, delivery_id, driver_id, vehicle_id)
            VALUES (:itemId, :shipperId, :pickupId, :destinationId, :driver, :vehicleId)
        """.trimIndent()
        val patternParamsFactory = SFM.newInstance().newSqlParameterSourceFactory(Pattern::class.java)
        destination.batchUpdate(insertSql,patternParamsFactory.newSqlParameterSources(patterns))
        return patterns
    }

}