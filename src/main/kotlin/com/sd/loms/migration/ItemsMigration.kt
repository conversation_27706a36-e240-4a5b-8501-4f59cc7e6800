package com.sd.loms.migration

import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate
import org.simpleflatmapper.jdbc.spring.JdbcTemplateMapperFactory as SFM

class ItemsMigration(
    private val source: NamedParameterJdbcTemplate,
    private val destination: NamedParameterJdbcTemplate
) : TableMigration {

    override fun migrate(): Any {
        val selectSql = """
            select
                nm name, snm1, snm2, category, rolly1_flg, rolly2_flg, rolly3_flg, bcolor,
                fcolor, sort, opt2, opt3, poison_flg, cast(item_cd as integer) roms_id,
                case when delete_flg = true then current_timestamp else null end deleted_at
            from m_items;
        """.trimIndent()

        val insertSql = """
            insert into m_items (
                name, snm1, snm2, category, rolly1_flg, rolly2_flg, rolly3_flg,
                bcolor, fcolor, sort, opt2, opt3, poison_flg, roms_id, deleted_at
            ) values (
                :name, :snm1, :snm2, :category, :rolly1Flg, :rolly2Flg, :rolly3Flg,
                :bcolor, :fcolor, :sort, :opt2, :opt3, :poisonFlg, :romsId, :deletedAt
            )
        """.trimIndent()

        val items = source.query(selectSql, SFM.newInstance().newRowMapper(MItem::class.java))
        val batch = SFM.newInstance().newSqlParameterSourceFactory(MItem::class.java).newSqlParameterSources(items)
        destination.batchUpdate(insertSql, batch)
        return items
    }
}