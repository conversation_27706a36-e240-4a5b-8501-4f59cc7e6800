package com.sd.loms.application

import com.sd.loms.domain.ports.DriverDataAccessPort
import jakarta.enterprise.context.ApplicationScoped

@ApplicationScoped
class DriverDataAccessUseCases(private val driverPort: DriverDataAccessPort) {

    fun getAllDrivers() = driverPort.getAllDrivers()
    fun getDriverById(id: Long) = driverPort.getDriverById(id)
    fun saveDriver(driver: com.sd.loms.domain.DriverDto) = driverPort.saveDriver(driver)
    fun updateDriver(driver: com.sd.loms.domain.DriverDto) = driverPort.updateDriver(driver)
    fun deleteDriver(id: Long) = driverPort.deleteDriver(id)
    fun getAllGroups() = driverPort.getAllGroups()
    fun getAllItems() = driverPort.getAllItems()
    fun getAllVehicles() = driverPort.getAllVehicles()
    fun getAllSalaries() = driverPort.getAllSalaries()
    fun getAllLocations() = driverPort.getAllLocations()
}
