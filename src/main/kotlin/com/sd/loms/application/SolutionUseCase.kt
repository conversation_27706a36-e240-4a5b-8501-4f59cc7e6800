package com.sd.loms.application

import com.sd.loms.domain.AllocationProblem
import com.sd.loms.domain.SolutionChangeValidationRequest
import com.sd.loms.domain.ports.SolutionPort
import jakarta.enterprise.context.ApplicationScoped
import java.time.LocalDate

@ApplicationScoped
class SolutionUseCase(private val solutionPort: SolutionPort) {
	fun solveAndListen(problem: AllocationProblem) = solutionPort.solveAndListen(problem)
	fun validateSolutionChange(validationRequest: SolutionChangeValidationRequest) = solutionPort.validateSolutionChange(validationRequest)
}
