package com.sd.loms.infrastructure.planning.adapter


import com.sd.loms.domain.AllocationRun
import com.sd.loms.domain.Solution
import jakarta.ws.rs.POST
import jakarta.ws.rs.PUT
import jakarta.ws.rs.Path
import org.eclipse.microprofile.rest.client.inject.RegisterRestClient


@Path("")
@RegisterRestClient
interface LomsPersistenceExternalService {

	@POST
	@Path("/api/v1/solutions")
	fun save(solution: Solution)


	@POST
	@Path("/api/v1/allocations")
	fun saveAllocationRun(allocationRun: AllocationRun): Long


	@PUT
	@Path("/api/v1/allocations/{id}")
	fun updateAllocationRun(id: Long, allocationRun: AllocationRun): AllocationRun
}
