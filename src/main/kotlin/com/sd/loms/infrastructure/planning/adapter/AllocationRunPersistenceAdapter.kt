package com.sd.loms.infrastructure.planning.adapter

import com.sd.loms.domain.AllocationRun
import com.sd.loms.domain.ports.AllocationRunPersistencePort
import jakarta.enterprise.context.ApplicationScoped
import org.eclipse.microprofile.rest.client.inject.RestClient


@ApplicationScoped
class AllocationRunPersistenceAdapter(@RestClient private val lomsPersistenceExternalService: LomsPersistenceExternalService) :
	AllocationRunPersistencePort {
	override fun save(allocationRun: AllocationRun): Long {
		return lomsPersistenceExternalService.saveAllocationRun(allocationRun)
	}

	override fun update(allocationRun: AllocationRun): AllocationRun {
		return lomsPersistenceExternalService.updateAllocationRun(allocationRun.id, allocationRun)
	}
}
