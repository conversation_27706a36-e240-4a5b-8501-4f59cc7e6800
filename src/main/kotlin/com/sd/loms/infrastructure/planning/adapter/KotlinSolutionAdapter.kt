package com.sd.loms.infrastructure.planning.adapter

import ai.timefold.solver.core.api.domain.solution.ConstraintWeightOverrides
import ai.timefold.solver.core.api.score.buildin.hardmediumsoftlong.HardMediumSoftLongScore
import ai.timefold.solver.core.api.solver.SolutionManager
import ai.timefold.solver.core.api.solver.SolutionUpdatePolicy
import ai.timefold.solver.core.api.solver.SolverManager
import com.sd.loms.domain.*
import com.sd.loms.domain.ports.AllocationRunPersistencePort
import com.sd.loms.domain.ports.AllocationRunPort
import com.sd.loms.domain.ports.DistanceMatrixPort
import com.sd.loms.domain.ports.SolutionDataAccessPort
import com.sd.loms.domain.ports.SolutionPersistencePort
import com.sd.loms.domain.ports.SolutionPort
import com.sd.loms.infrastructure.data.adapter.AllocationRunAdapter
import com.sd.loms.infrastructure.data.adapter.SolutionDataAccessAdapter
import com.sd.loms.infrastructure.data.entity.OrderEntity
import com.sd.loms.infrastructure.data.repo.*
import com.sd.loms.infrastructure.planning.adapter.model.VehicleRoutingSolution
import jakarta.enterprise.context.ApplicationScoped
import org.apache.commons.lang3.ObjectUtils
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import java.math.BigDecimal
import java.time.LocalDate
import java.util.concurrent.TimeUnit
import com.sd.loms.domain.Location as DomainLocation
import com.sd.loms.infrastructure.planning.adapter.model.Driver as PlanningDriver
import com.sd.loms.infrastructure.planning.adapter.model.Order as PlanningOrder
import com.sd.loms.infrastructure.planning.adapter.model.Vehicle as PlanningVehicle

@ApplicationScoped
class KotlinSolutionAdapter(
	private val allocationRunRepo: AllocationRunPort,
	private val solutionRepo: SolutionDataAccessPort,
	private val solutionManager: SolutionManager<VehicleRoutingSolution, HardMediumSoftLongScore>,
	private val solverManager: SolverManager<VehicleRoutingSolution, Long>,
) : SolutionPort {

	companion object {
		private val log: Logger = LoggerFactory.getLogger(this::class.java)
	}

	override fun solveAndListen(problem: AllocationProblem): Result<Long> {
		if (solutionRepo.isThereAnySolverInProgress()) {
			return Result.failure(Exception("Solver is already running"))
		}

		val distanceMatrixPort = KotlinDistanceMatrixAdapter()
		distanceMatrixPort.populateDistanceMatrixFromDB(problem.locations, problem.distances)
		val fulfillDate = problem.orders[0].fulfillDate
		val allocationRun = allocationRunRepo.save(AllocationRun(0, RunStatus.SOLVING, fulfillDate, problem.withHistory, problem.withEvenAllocation))

		val orders = toPlanningOrders(problem)
		val vehicles = getVehicleDrivers(distanceMatrixPort, problem.locations.filter { it.depot }[0], problem)

		val constraintWeightMap = mutableMapOf<String, HardMediumSoftLongScore>()
		// overriding the default weights with ZERO means that the solver will not consider these constraints
		if (!problem.withHistory) {
			constraintWeightMap.put("The pattern found in the past", HardMediumSoftLongScore.ZERO)
		}
		if(!problem.withEvenAllocation) {
			constraintWeightMap.put("ドライバーごとに均等なっていません。", HardMediumSoftLongScore.ZERO)
		}

		val drivers = vehicles.map { it.driver }.toSet()
		solverManager.solveBuilder()
			.withProblemId(allocationRun.id)
			.withProblem(VehicleRoutingSolution(vehicles, orders, drivers.toList(), ConstraintWeightOverrides.of(constraintWeightMap)))
			.withBestSolutionConsumer { saveIfItIsAFeasibleSolution(it, allocationRun) }
			.withFinalBestSolutionConsumer { allocationRunRepo.update(allocationRun.copy(status = RunStatus.COMPLETED)) }
			.run()
		return Result.success(allocationRun.id)
	}


	override fun validateSolutionChange(request: SolutionChangeValidationRequest): SolutionChangeValidationResponse {
		val fromResult = request.fromProblem?.let {
			val distanceMatrixPort = KotlinDistanceMatrixAdapter()
			distanceMatrixPort.populateDistanceMatrixFromDB(request.fromProblem.locations, request.fromProblem.distances)
			val vehicles = getVehicleDrivers(distanceMatrixPort, request.fromProblem.locations.filter { it.depot }[0], request.fromProblem)
			val drivers = vehicles.map { it.driver }.toSet()
			val fromVehicle = getVehicleAndOrders(request.fromProblem)
			val fromAnalyzedSolution = makeAndAnalyzeTheSolution(drivers.toList(), fromVehicle, fromVehicle.orders, emptyList(), true)
			getSolutionValidation(fromAnalyzedSolution, request.runId, request.solutionId)
		} ?: SolutionValidation(true, listOf(), null)


		val distanceMatrixPortToProblem = KotlinDistanceMatrixAdapter()
		distanceMatrixPortToProblem.populateDistanceMatrixFromDB(request.toProblem.locations, request.toProblem.distances)
		val vehicles = getVehicleDrivers(distanceMatrixPortToProblem, request.toProblem.locations.filter { it.depot }[0], request.toProblem)
		val drivers = vehicles.map { it.driver }.toSet()

		val toVehicle = getVehicleAndOrders(request.toProblem)
		val toAnalyzedSolution = makeAndAnalyzeTheSolution(drivers.toList(), toVehicle, toVehicle.orders, request.changes)
		val toResult = getSolutionValidation(toAnalyzedSolution, request.runId, request.solutionId)

		val solution = if (fromResult.isValid && toResult.isValid) {
			val detailsList = mutableListOf<SolutionDetails>()
			if (fromResult.solution != null) {
				detailsList.addAll(fromResult.solution.solutionDetails)
			}
			detailsList.addAll(toResult.solution!!.solutionDetails)
			Solution(null, request.runId, 1L, toResult.solution.score, 1, detailsList, true, request.solutionId)
		} else null

		return SolutionChangeValidationResponse(
			fromResult.isValid && toResult.isValid,
			toResult.messages,
			solution
		)
	}

	private fun getAnalyzedSolution(drivers: List<PlanningDriver>, vehicle: PlanningVehicle): AnalysisResult {
		val solution = VehicleRoutingSolution(listOf(vehicle), vehicle.orders, drivers,ConstraintWeightOverrides.of(mapOf()))
		val score = solutionManager.update(solution, SolutionUpdatePolicy.UPDATE_ALL)
		val analysis = solutionManager.analyze(solution)
		val explanation = solutionManager.explain(solution)
		val messages =
			explanation.constraintMatchTotalMap.filter { !it.value.score.isFeasible && it.value.score.hardScore() < 0L }
				.keys.map { it.replace("com.sd.loms.infrastructure.planning.adapter.model/", "") }
		return AnalysisResult(solution, score!!, analysis, messages)
	}

	private fun toPlanningOrders(problem: AllocationProblem) = problem.orders.map { order ->
		val pickup = problem.locations.find { it.id == order.pickupId }!!
		val delivery = problem.locations.find { it.id == order.deliveryId }!!
		val item = problem.items.find { it.id == order.itemId }!!
		PlanningOrder(order.id, pickup, delivery, item, order.shipperId, order.weight, order.litres)
	}


	private fun getVehicleAndOrders(problem: AllocationProblem): PlanningVehicle {
		val distanceMatrix = KotlinDistanceMatrixAdapter()
		distanceMatrix.populateDistanceMatrixFromDB(problem.locations, problem.distances)
		val orders = toPlanningOrders(problem)
		val vehicles = getVehicleDrivers(distanceMatrix, problem.locations.filter { it.depot }[0], problem)
		val vehicle = vehicles[0] // Assuming there's only one vehicle
		vehicle.orders = orders as MutableList<PlanningOrder>
		return vehicle
	}

	private fun getSolutionValidation(analysisResult: AnalysisResult, runId: Long, solutionId: Long):
		SolutionValidation {
		val hardScore = analysisResult.score.hardScore()
		val isValid = hardScore == 0L
		var solution: Solution? = null
		if (isValid) {
			solution = toDomainSolution(analysisResult.solution, runId, solutionId)
		}
		return SolutionValidation(isValid, analysisResult.messages, solution)
	}

	private fun makeAndAnalyzeTheSolution(
		drivers: List<PlanningDriver>, vehicle: PlanningVehicle, orders: List<PlanningOrder>, changes: List<IndexDetails>, forOld: Boolean = false
	): AnalysisResult {
		vehicle.orders = orders.sortedBy { order ->
			val indexDetails = changes.find { it.jobId == order.id }
			if (forOld) indexDetails?.oldIndex else indexDetails?.newIndex ?: Int.MAX_VALUE
		} as MutableList<PlanningOrder>

		return getAnalyzedSolution(drivers, vehicle)
	}

	private fun getVehicleDrivers(distanceMatrixPort: DistanceMatrixPort, depot: DomainLocation, problem: AllocationProblem) =
		//.filter { vehicle.id in it.canDrive || vehicle.id == it.primaryVehicle }
		problem.vehicles.flatMap { dto ->
			val vehicle = dto.toDomainVehicle(problem.items)
			problem.drivers
				.filter { (it.primaryVehicle == 0L && vehicle.id in it.canDrive) || vehicle.id == it.primaryVehicle }
				.map { driverDro ->
					val driver = getPlanningDriver(driverDro, vehicle, problem.items)
					PlanningVehicle(
						"${vehicle.id}-${driver.id}", vehicle.id, vehicle.name,
						vehicle.frontTankCapacity, vehicle.rearTankCapacity, vehicle.totalTankCapacity,
						vehicle.weightCapacity, vehicle.canHandle, depot, driver, distanceMatrixPort, problem.patterns
					)
				}
		}

	private fun getPlanningDriver(it: DriverDto, vehicle: Vehicle, items: List<Item>): PlanningDriver {
		val canDrive = if (vehicle.id == it.primaryVehicle) {
			setOf(it.primaryVehicle)
		} else {
			it.canDrive
		}
		return PlanningDriver(it.id, it.name, it.primaryVehicle, toItems(it.canHandle, items), canDrive)
	}

	private fun saveIfItIsAFeasibleSolution(solution: VehicleRoutingSolution, run: AllocationRun) {
		val domainSolution = toDomainSolution(solution, runId = run.id, oldSolutionId = 0)
		solutionRepo.save(domainSolution)
	}

	private fun toDomainSolution(solution: VehicleRoutingSolution, runId: Long, oldSolutionId: Long): Solution {
		solutionManager.update(solution, SolutionUpdatePolicy.UPDATE_SCORE_ONLY) // Sets the score

		// TODO need to see if this can be persisted
		val scoreAnalysis = solutionManager.analyze(solution)
		solutionManager.explain(solution)

		val vehicleRoutes = extractTotalTimeAndSolutionData(solution)
		val totalTime = vehicleRoutes.map { it.vehicle.time }.sum()
		val readableTotalTime = readableTotalTime(totalTime)
		val notFeasible = isNotFeasible(vehicleRoutes, solution.score!!, readableTotalTime)
		if (!notFeasible) { // feasible. not feasible solutions are logged with reason already
			log.info("Solution is here. Score: %-25s Time: %-10s".format(solution.score, readableTotalTime))
		}

		val details = vehicleRoutes.flatMap { vehicleRoute ->
			vehicleRoute.visits.map {
				SolutionDetails(
					vehicleId = vehicleRoute.vehicle.id, driverId = vehicleRoute.vehicle.driverId, orderId = it.jobId,
					visitIndex = it.visitIndex, uniqueId = it.uniqueId,
					it.arrivalTime, it.serviceDuration, it.departureTime, it.distanceToDepot ?: 0
				)
			}
		}

		val manual = oldSolutionId != 0L
		return Solution(null, runId, totalTime, solution.score.toString(), 1, details, manual, oldSolutionId)
	}

	private fun extractTotalTimeAndSolutionData(solution: VehicleRoutingSolution) = solution.orders
		.groupBy { it.vehicle }
		.map { (v, o) ->
			val orders = o.sortedBy { it.getArrivalTimeAtPickup() }
			val d = v.depot
			val depot = VisitVM(d.id, d.uniqueId, orders[0].id, VisitType.DEPOT, 0, "", 32400000L, 0L, 32400000L)
			val visits = mutableListOf<VisitVM>(depot)

			orders.forEachIndexed { index, it ->
				val pickup = VisitVM(
					it.pickup.id, it.pickup.uniqueId, it.id, VisitType.PICKUP, (index * 2) + 1, it.item.name,
					it.getArrivalTimeAtPickup(), it.pickup.serviceDuration, it.getDepartureTimeAtPickup(),
					null, it.getTravelTimeToPickup()
				)
				val delivery = VisitVM(
					it.delivery.id, it.delivery.uniqueId, it.id, VisitType.DELIVERY, (index * 2) + 2, it.item.name,
					it.getArrivalTimeAtDelivery(), it.delivery.serviceDuration, it.getDepartureTimeAtDelivery(),
					if (it.isLastOrder()) it.getTimeToDepot() else null, it.getTravelTimeToDelivery()
				)
				visits.add(pickup)
				visits.add(delivery)
			}
			val timeTill = visits.mapNotNull { it.distanceFromPreviousVisit }.sum()

			val lastOrder = orders.last()
			val depotVmFinal = VisitVM(
				d.id, d.uniqueId, orders[0].id, VisitType.DEPOT, visits.size, "",
				(lastOrder.getDepartureTimeAtDelivery()) + lastOrder.getTimeToDepot(), 0L, 0L
			)
			visits.add(depotVmFinal)
			val time = timeTill + lastOrder.getTimeToDepot()
			val vehicle = VehicleVM(v.vehicleId, v.driver.id, v.canHandle.map { it.name }.toSet(), time)
			VehicleRouteVM(vehicle, visits)
		}

	private fun readableTotalTime(millis: Long?): String {
		millis ?: return ""
		val hours = TimeUnit.MILLISECONDS.toHours(millis)
		val minutes = TimeUnit.MILLISECONDS.toMinutes(millis) % TimeUnit.HOURS.toMinutes(1)
		val seconds = TimeUnit.MILLISECONDS.toSeconds(millis) % TimeUnit.MINUTES.toSeconds(1)
		return "%02d%s %02d%s".format(hours, "H", minutes, "M")
	}

	private fun isNotFeasible(finalData: List<VehicleRouteVM>, score: HardMediumSoftLongScore, time: String): Boolean {
		val notFeasible = finalData.any { r ->

			val isToVehicleCapabilityMismatch = hasToVehicleCapabilityMismatch(r, score, time)
			if (isToVehicleCapabilityMismatch) return@any true

			val hasOddNumberOfVisits = hasOddNumberOfVisits(r, score, time)
			if (hasOddNumberOfVisits) return@any true

			val isPickupAndDeliveryByDifferentVehicle = hasPickupAndDeliveryConflict(r, score, time)
			if (isPickupAndDeliveryByDifferentVehicle) return@any true

			isReturnToDepotLaterThan7Pm(r, score, time)
		}
		return notFeasible
	}

	private fun hasToVehicleCapabilityMismatch(r: VehicleRouteVM, score: HardMediumSoftLongScore, time: String): Boolean {
		val notFeasible = r.visits.filter { it.item != "" } // filter out depot
			.any { it.item !in r.vehicle.canHandle }
		if (notFeasible) {
			val categories = r.visits
				.filter { !ObjectUtils.isEmpty(it.item) }
				.map { visitVM -> visitVM.jobId.toString() + "-" + visitVM.item }
			val msg =
				"Solution is here. Score: %-25s Time: %-10s - Not feasible due to vehicle type %-25s %-25s, Jobs: %-10s"
			log.info(msg.format(score, time, r.vehicle.id, r.vehicle.canHandle, categories))
		}
		return notFeasible
	}

	private fun hasOddNumberOfVisits(r: VehicleRouteVM, score: HardMediumSoftLongScore, time: String): Boolean {
		val notFeasible = r.visits.size % 2 != 0
		if (notFeasible) {
			val msg = "Solution is here. Score: %-25s Time: %-10s - Not feasible due to odd number of visits: %-10s"
			log.info(msg.format(score, time, r.vehicle.id))
		}
		return notFeasible
	}

	private fun hasPickupAndDeliveryConflict(r: VehicleRouteVM, score: HardMediumSoftLongScore, time: String): Boolean {
		var i = 1
		while (i < r.visits.size - 1) {
			val pickup = r.visits[i]
			val delivery = r.visits[i + 1]

			if (pickup.type != VisitType.PICKUP || delivery.type != VisitType.DELIVERY || pickup.jobId != delivery.jobId) {
				val msg = if (pickup.jobId != delivery.jobId) {
					"Solution is here. Score: %-25s Time: %-10s - Not feasible due to wrong jobId: %-25s"
				} else {
					"Solution is here. Score: %-25s Time: %-10s - Not feasible due to wrong visit type: %-25s"
				}
				log.info(msg.format(score, time, r.vehicle.id))
				return true
			}
			i += 2
		}
		return false
	}

	private fun isReturnToDepotLaterThan7Pm(r: VehicleRouteVM, score: HardMediumSoftLongScore, time: String): Boolean {
		val lastVisit = r.visits.last { it.distanceToDepot != null }
		val notFeasible = lastVisit.departureTime + lastVisit.distanceToDepot!! > 68400000L
		if (notFeasible) {
			val msg =
				"Solution is here. Score: %-25s Time: %-10s - Not feasible due to time to deopt is beyond 7PM: %-25s"
			log.info(msg.format(score, time, r.vehicle.id))
		}
		return notFeasible
	}
}
