package com.sd.loms.infrastructure.planning.adapter

import com.sd.loms.domain.Coordinates
import com.sd.loms.domain.Distance
import com.sd.loms.domain.DistanceDto
import com.sd.loms.domain.Location
import com.sd.loms.domain.ports.DistanceMatrixPort
import java.util.concurrent.ConcurrentHashMap

class KotlinDistanceMatrixAdapter : DistanceMatrixPort {
	private val matrix: MutableMap<Coordinates, MutableMap<Coordinates, Distance>> = HashMap()


	override fun distance(from: Coordinates, to: Coordinates): Distance {
		if (from == to) {
			return Distance(0)
		}
		require(matrix.containsKey(from)) { "Unknown 'from' location ($from)" }
		val distanceRow: Map<Coordinates, Distance> = matrix[from]!!
		require(distanceRow.containsKey(to)) { "Unknown 'to' location ($to)" }
		return distanceRow[to]!!
	}

	override fun put(from: Coordinates, to: Coordinates, distance: Distance) {
		matrix.computeIfAbsent(from) { _: Coordinates -> ConcurrentHashMap<Coordinates, Distance>() }[to] = distance
	}

	override fun removeLocation(location: Coordinates) {
		// Remove the distance matrix row (distances from the removed location to others).
		matrix.remove(location)
		// XTODO also remove the "column" of the matrix (distances from others to the removed location) to avoid memory
		//  leak.
		//  But this probably requires making DistanceMatrixRow immutable (otherwise there's a risk of NPEs in solver)
		//  and update PlanningLocations' distance maps through problem fact changes.
	}

	override fun clear() {
		matrix.clear()
	}

	/**
	 * Number of rows in the matrix.
	 *
	 * @return number of rows
	 */
	fun dimension(): Int {
		return matrix.size
	}


	override fun populateDistanceMatrixFromDB(locations: List<Location>, distances: List<DistanceDto>) {
		locations.stream()
			.forEach { from ->
				locations.stream()
					.filter { to -> to.id != from.id }
					.forEach { to ->
						val distance = distances.stream()
							.filter { it.fromLoc == from?.id && it.toLoc == to?.id }
							.findFirst()
							.orElseThrow { IllegalStateException("Distance from: [$from] to: [$to] is missing in the distance repository. This should not happen.") }
						this.put(from.getCoordinates(), to.getCoordinates(), Distance(distance.distance))
					}
			}
	}
}
