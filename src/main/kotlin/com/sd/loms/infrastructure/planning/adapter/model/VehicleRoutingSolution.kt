package com.sd.loms.infrastructure.planning.adapter.model

import ai.timefold.solver.core.api.domain.solution.ConstraintWeightOverrides
import ai.timefold.solver.core.api.domain.solution.PlanningEntityCollectionProperty
import ai.timefold.solver.core.api.domain.solution.PlanningScore
import ai.timefold.solver.core.api.domain.solution.PlanningSolution
import ai.timefold.solver.core.api.domain.solution.ProblemFactCollectionProperty
import ai.timefold.solver.core.api.domain.valuerange.ValueRangeProvider
import ai.timefold.solver.core.api.score.buildin.hardmediumsoftlong.HardMediumSoftLongScore

@PlanningSolution
data class VehicleRoutingSolution(
	@PlanningEntityCollectionProperty @ValueRangeProvider(id = "vehicleRange") var vehicles: List<Vehicle>,
	@ValueRangeProvider(id = "orderRange") var orders: List<Order>,
	@ProblemFactCollectionProperty @ValueRangeProvider(id = "driverRange") var drivers: List<Driver>,
	var overrides: ConstraintWeightOverrides<HardMediumSoftLongScore>? = null,
	@PlanningScore var score: HardMediumSoftLongScore? = null
)
