package com.sd.loms.infrastructure.planning.adapter

import com.sd.loms.domain.Solution
import com.sd.loms.domain.ports.SolutionPersistencePort
import jakarta.enterprise.context.ApplicationScoped
import org.eclipse.microprofile.rest.client.inject.RestClient

@ApplicationScoped
class SolutionPersistenceAdapter(
	@RestClient
	private val lomsPersistenceExternalService: LomsPersistenceExternalService
) : SolutionPersistencePort {

	override fun save(solution: Solution) {
		lomsPersistenceExternalService.save(solution)
	}

}
