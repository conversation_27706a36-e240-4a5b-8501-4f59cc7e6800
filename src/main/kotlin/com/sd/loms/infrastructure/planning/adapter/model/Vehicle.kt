package com.sd.loms.infrastructure.planning.adapter.model

import ai.timefold.solver.core.api.domain.entity.PlanningEntity
import ai.timefold.solver.core.api.domain.lookup.PlanningId
import ai.timefold.solver.core.api.domain.variable.PlanningListVariable
import com.sd.loms.domain.Item
import com.sd.loms.domain.Location
import com.sd.loms.domain.PatternDto
import com.sd.loms.domain.ports.DistanceMatrixPort

@PlanningEntity
data class Vehicle(
	@PlanningId var id: String,
	val vehicleId: Long,
	val name: String,
	val frontTankCapacity: Int,
	val rearTankCapacity: Int,
	val totalTankCapacity: Int,
	val weightCapacity: Int,
	val canHandle: Set<Item>,
	val depot: Location,
	val driver: Driver,
	val distanceMatrix: DistanceMatrixPort,
	val patterns: List<PatternDto>,

	@PlanningListVariable(valueRangeProviderRefs = ["orderRange"])
	var orders: MutableList<Order> = mutableListOf()
) {
	fun getNumberOfTanks() = if (frontTankCapacity == 0 && rearTankCapacity == 0) {
		1
	} else {
		2
	}
}
