package com.sd.loms.infrastructure.planning.adapter

import ai.timefold.solver.core.api.score.buildin.hardmediumsoftlong.HardMediumSoftLongScore
import ai.timefold.solver.core.api.score.stream.Constraint
import ai.timefold.solver.core.api.score.stream.ConstraintFactory
import ai.timefold.solver.core.api.score.stream.ConstraintProvider
import ai.timefold.solver.core.api.score.stream.Joiners
import com.sd.loms.infrastructure.planning.adapter.model.Driver
import com.sd.loms.infrastructure.planning.adapter.model.Order
import com.sd.loms.infrastructure.planning.adapter.model.Vehicle
import kotlin.math.abs

class ScheduleConstraintProvider : ConstraintProvider {
    override fun defineConstraints(factory: ConstraintFactory): Array<Constraint> {
        return arrayOf(
            // Hard Constraints
            vehicleMustHaveOneDriver(factory), // This check is required as we mix and match Vehicles and drivers combinations
			driverMustHaveOneVehicle(factory), // This as well

            vehicleWeightCapacityCheck(factory),
            vehicleTankCapacityCheck(factory),
            vehicleAndDriverShouldBeCapableOfHandlingTheOrderItem(factory),
            ifVehicleIsCarryingCleaningItemThenItShouldNotCarryOtherItemsOnThatDay(factory),
            driverMustDrivePrimaryVehicle(factory),
            driverShouldBeCapableOfDrivingTheVehicle(factory),
            enforceDepotReturnNotLaterThan7PM(factory),
            enforceTimeWindowsForPickupAndDelivery(factory),

			// Medium Constraints
			checkIfPatternFoundButDriverOrVehicleNotMatchingWithIt(factory),
			utilizeMaximumDrivers(factory),

            // Soft constraints
            minimizeTotalTravelTime(factory),
            preferSingleTankVehicle(factory),
            //splitAcrossVehicles(factory),
        )
    }

    // This check is required as we mix and match Vehicles and drivers combinations
    private fun vehicleMustHaveOneDriver(factory: ConstraintFactory): Constraint {
        return factory.forEachUniquePair(Vehicle::class.java, Joiners.equal(Vehicle::vehicleId))
            .filter(bothHasOrders)
            .filter { a, b -> a.driver.id != b.driver.id } // Vehicle with more than one driver having orders
            .penalize(HardMediumSoftLongScore.ONE_HARD)
            .asConstraint("車両には1人の運転手しか割り当てられません。")
    }

	// This check is required as we mix and match Vehicles and drivers combinations
	private fun driverMustHaveOneVehicle(factory: ConstraintFactory): Constraint {
		return factory.forEachUniquePair(Vehicle::class.java)
			.filter(bothHasOrders)
			.filter { a, b -> a.vehicleId != b.vehicleId && a.driver.id == b.driver.id }
			.penalize(HardMediumSoftLongScore.ONE_HARD)
			.asConstraint("運転手には1台の車両しか割り当てられません。")
	}

    private fun vehicleWeightCapacityCheck(factory: ConstraintFactory): Constraint {
        return factory.forEach(Vehicle::class.java)
            .filter { it.orders.any { order -> order.vehicle.weightCapacity < order.weight } }
            .penalize(HardMediumSoftLongScore.ONE_HARD)
            .asConstraint("車両の積載容量がオーバーしています。")
    }

    private fun vehicleTankCapacityCheck(factory: ConstraintFactory): Constraint {
        return factory.forEach(Vehicle::class.java)
            .filter { it.orders.any { order -> order.vehicle.totalTankCapacity < order.litres } }
            .penalize(HardMediumSoftLongScore.ONE_HARD)
            .asConstraint("車両のタンク容量がオーバーしています。")
    }

    private fun vehicleAndDriverShouldBeCapableOfHandlingTheOrderItem(factory: ConstraintFactory): Constraint {
        return factory.forEach(Vehicle::class.java)
            .filter {
                val vehicleCantHandle = it.orders.any { order -> !it.canHandle.contains(order.item) }
                val driverCantHandle = it.orders.any { order -> !it.driver.canHandle.contains(order.item) }
                vehicleCantHandle || driverCantHandle
            }
            .penalize(HardMediumSoftLongScore.ONE_HARD)
            .asConstraint("車両または運転手が注文アイテムを処理できません。")
    }

    private fun ifVehicleIsCarryingCleaningItemThenItShouldNotCarryOtherItemsOnThatDay(factory: ConstraintFactory): Constraint {
        return factory.forEach(Vehicle::class.java)
            .filter {
                val cleaningRequiredItem = it.orders.firstOrNull { order -> order.item.cleaningFlag }
                if (cleaningRequiredItem != null) {
                    return@filter it.orders.any { order -> order.item.id != cleaningRequiredItem.item.id } // Orders with other items
                }
                false
            }
            .penalize(HardMediumSoftLongScore.ONE_HARD)
            .asConstraint("清掃アイテムを積んでいる場合、他のアイテムを積むことはできません。")
    }

    private fun driverMustDrivePrimaryVehicle(factory: ConstraintFactory): Constraint {
        return factory.forEach(Vehicle::class.java)
            .filter { hasOrders(it) && primaryVehicleAvailable(it) && currentDriverIsNotPrimaryDriver(it) }
            .penalize(HardMediumSoftLongScore.ONE_HARD)
            .asConstraint("運転手はプライマリ車両を運転しなければなりません。")
    }

    private fun driverShouldBeCapableOfDrivingTheVehicle(factory: ConstraintFactory): Constraint {
        return factory.forEach(Vehicle::class.java)
            .filter { hasOrders(it) && noPrimaryVehicleAvailable(it) && driverCantDriveVehicle(it) }
            .penalize(HardMediumSoftLongScore.ONE_HARD)
            .asConstraint("運転手はその車両を運転できません。")
    }

    private fun enforceDepotReturnNotLaterThan7PM(factory: ConstraintFactory): Constraint {
        return factory.forEach(Vehicle::class.java)
            .filter(hasOrders)
            .filter {
                val lastOrder = getLastOrder(it) // Not null
                val returnTime = (lastOrder?.getDepartureTimeAtDelivery() ?: 0L) + (lastOrder?.getTimeToDepot() ?: 0L)
                return@filter returnTime > 68400000L // 7PM is 68400000 milliseconds from 12:00 AM
            }
            .penalize(HardMediumSoftLongScore.ONE_HARD)
            .asConstraint("車両は午後7時前にデポに戻らなければなりません。")
    }

    private fun enforceTimeWindowsForPickupAndDelivery(factory: ConstraintFactory): Constraint {
        return factory.forEach(Vehicle::class.java)
            .filter {
                it.orders.any { order ->
                    val arrivalAtPickup = order.getArrivalTimeAtPickup()
                    val arrivalAtDelivery = order.getArrivalTimeAtDelivery()

                    val laterAtPickup = (order.pickup.dueTime - order.pickup.serviceDuration) < arrivalAtPickup
                    val lateAtDelivery = (order.delivery.dueTime - order.delivery.serviceDuration) < arrivalAtDelivery
                    laterAtPickup || lateAtDelivery
                }
            }
            .penalize(HardMediumSoftLongScore.ONE_HARD)
            .asConstraint("ピックアップと届け先の時間ウィンドウを守る必要があります。")
    }

	private fun checkIfPatternFoundButDriverOrVehicleNotMatchingWithIt(factory: ConstraintFactory): Constraint {
		return factory.forEach(Order::class.java)
			.filter( patternFoundButDriverOrVehicleNotMatchingWithIt)
			.penalize(HardMediumSoftLongScore.ONE_MEDIUM)
			.asConstraint("The pattern found in the past")
	}

	private fun utilizeMaximumDrivers(factory: ConstraintFactory): Constraint {
		return factory.forEach(Driver::class.java)
			.ifNotExists(Vehicle::class.java,
				Joiners.equal(Driver::id) { vehicle -> vehicle.driver.id },
				Joiners.filtering { _, vehicle -> vehicle.orders.isNotEmpty() }
			)
			.penalize(HardMediumSoftLongScore.ONE_MEDIUM)
			.asConstraint("ドライバーごとに均等なっていません。")
	}

	val patternFoundButDriverOrVehicleNotMatchingWithIt : (Order)-> Boolean = { order ->
		val found = order.vehicle.patterns.find {
			it.itemId == order.item.id &&
			it.shipperId == order.shipperId &&
			it.pickupId == order.pickup.id &&
			it.deliveryId == order.delivery.id
		}
		if (found != null) {
			found.vehicleId != order.vehicle.vehicleId || found.driverId != order.vehicle.driver.id
		}
		false
	}

    private fun minimizeTotalTravelTime(factory: ConstraintFactory): Constraint {
        return factory.forEach(Vehicle::class.java)
            .filter(hasOrders)
            .penalizeLong(HardMediumSoftLongScore.ONE_SOFT) {
                val lastOrder = getLastOrder(it) // Not null
                (lastOrder?.getDepartureTimeAtDelivery() ?: 0L) + (lastOrder?.getTimeToDepot() ?: 0L)
            }
            .asConstraint("移動時間を最小化する。")
    }

    private fun preferSingleTankVehicle(factory: ConstraintFactory): Constraint {
        return factory.forEach(Vehicle::class.java)
            .filter { it.getNumberOfTanks() > 1 }
            .penalize(HardMediumSoftLongScore.ONE_SOFT)
            .asConstraint("車両は複数のタンクを持たない方が良い。")
    }

    private fun splitAcrossVehicles(factory: ConstraintFactory): Constraint {
        return factory.forEach(Vehicle::class.java)
            .filter { abs(it.orders.size - 10) > 1 }
            .penalizeLong(HardMediumSoftLongScore.ONE_SOFT) { abs(it.orders.size - 10).toLong() }
            .asConstraint("車両ごとの注文数が大きく偏っています。")
    }

    private fun getLastOrder(vehicle: Vehicle) = vehicle.orders.firstOrNull { it.isLastOrder() }

    val bothHasOrders: (Vehicle, Vehicle) -> Boolean = { a, b -> a.orders.isNotEmpty() && b.orders.isNotEmpty() }
    val hasOrders: (Vehicle) -> Boolean = { it.orders.isNotEmpty() }
    val noPrimaryVehicleAvailable: (Vehicle) -> Boolean = { it.driver.primaryVehicle == 0L }
    val primaryVehicleAvailable: (Vehicle) -> Boolean = { it.driver.primaryVehicle > 0L }
    val driverCantDriveVehicle: (Vehicle) -> Boolean = { !it.driver.canDrive.contains(it.vehicleId) }
    val currentDriverIsNotPrimaryDriver: (Vehicle) -> Boolean = { it.driver.primaryVehicle != it.vehicleId }
}
