package com.sd.loms.infrastructure.planning.adapter.model

import ai.timefold.solver.core.api.domain.entity.PlanningEntity
import ai.timefold.solver.core.api.domain.lookup.PlanningId
import ai.timefold.solver.core.api.domain.variable.InverseRelationShadowVariable
import ai.timefold.solver.core.api.domain.variable.PreviousElementShadowVariable
import ai.timefold.solver.core.api.domain.variable.NextElementShadowVariable
import com.sd.loms.domain.Item
import com.sd.loms.domain.Location

@PlanningEntity
data class Order(
	@PlanningId var id: Long,
	var pickup: Location,
	var delivery: Location,
	var item: Item,
	var shipperId: Long,
	var weight: Int,
	var litres: Int,
) {

	@InverseRelationShadowVariable(sourceVariableName = "orders") lateinit 	var vehicle: Vehicle
	@PreviousElementShadowVariable(sourceVariableName = "orders") 			var previousOrder: Order? = null
	@NextElementShadowVariable(sourceVariableName = "orders") 				var nextOrder: Order? = null

	fun getTravelTimeToPickup(): Long {
		val previousLoc = previousOrder?.delivery ?: vehicle.depot
		return vehicle.distanceMatrix.distance(previousLoc.getCoordinates(), pickup.getCoordinates()).millis
	}

	fun getTravelTimeToDelivery() =
		vehicle.distanceMatrix.distance(pickup.getCoordinates(), delivery.getCoordinates()).millis

	fun getArrivalTimeAtPickup(): Long {
		val previousDepartureTime = previousOrder?.getDepartureTimeAtDelivery() ?: vehicle.depot.readyTime
		return previousDepartureTime + getTravelTimeToPickup()
	}

	fun getDepartureTimeAtPickup() = getArrivalTimeAtPickup() + pickup.serviceDuration

	fun getArrivalTimeAtDelivery() = getDepartureTimeAtPickup() + getTravelTimeToDelivery()

	fun getDepartureTimeAtDelivery() = getArrivalTimeAtDelivery() + delivery.serviceDuration

	fun getTimeToDepot() =
		vehicle.distanceMatrix.distance(delivery.getCoordinates(), vehicle.depot.getCoordinates()).millis

	fun isLastOrder() = nextOrder == null
}
