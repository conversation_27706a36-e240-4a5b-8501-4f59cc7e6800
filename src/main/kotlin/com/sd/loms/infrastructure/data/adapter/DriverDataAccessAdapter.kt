package com.sd.loms.infrastructure.data.adapter

import com.sd.loms.domain.DriverDto
import com.sd.loms.domain.Item
import com.sd.loms.domain.Vehicle
import com.sd.loms.domain.ports.DriverDataAccessPort
import com.sd.loms.presentation.dto.GroupData
import com.sd.loms.presentation.dto.LocationData
import com.sd.loms.presentation.dto.SalaryData
import com.sd.loms.infrastructure.data.EnableSoftDeleteFilter
import com.sd.loms.infrastructure.data.repo.*
import jakarta.enterprise.context.ApplicationScoped
import jakarta.transaction.Transactional

@ApplicationScoped
@Transactional
@EnableSoftDeleteFilter
class DriverDataAccessAdapter(
    private val driverRepository: DriverRepository,
    private val groupRepository: GroupRepository,
    private val itemRepository: ItemRepository,
    private val vehicleRepository: VehicleRepository,
    private val salaryRepository: SalaryRepository,
    private val locationRepository: LocationRepository
) : DriverDataAccessPort {

    override fun getAllDrivers(): List<DriverDto> {
        return driverRepository.listAll().map { it.toDomain() }
    }

    override fun getDriverById(id: Long): DriverDto? {
        return driverRepository.findById(id)?.toDomain()
    }

    override fun saveDriver(driver: DriverDto): Long {
        val entity = driverRepository.findById(driver.id) ?: com.sd.loms.infrastructure.data.entity.DriverEntity()
        // Map domain to entity
        entity.nm1 = driver.name
        entity.groupId = 1L // Default group for now
        entity.locationId = 1L // Default location for now
        entity.createdAt = System.currentTimeMillis()

        driverRepository.persist(entity)
        return entity.id!!
    }

    override fun updateDriver(driver: DriverDto): DriverDto {
        val entity = driverRepository.findById(driver.id) ?: throw RuntimeException("Driver not found")
        // Map domain to entity
        entity.nm1 = driver.name
        entity.updatedAt = System.currentTimeMillis()

        driverRepository.persist(entity)
        return entity.toDomain()
    }

    override fun deleteDriver(id: Long) {
        val entity = driverRepository.findById(id) ?: throw RuntimeException("Driver not found")
        driverRepository.delete(entity)
    }

    override fun getAllGroups(): List<GroupData> {
        return groupRepository.listAll().map { GroupData(it.id ?: 0L, it.nm ?: "") }
    }

    override fun getAllItems(): List<Item> {
        return itemRepository.listAll().map { it.toDomain() }
    }

    override fun getAllVehicles(): List<Vehicle> {
        return vehicleRepository.listAll().map { it.toDomain() }
    }

    override fun getAllSalaries(): List<SalaryData> {
        return salaryRepository.listAll().map { SalaryData(it.id ?: 0L, it.clazz.toString(), it.rank.toString()) }
    }

    override fun getAllLocations(): List<LocationData> {
        return locationRepository.listAll().map { LocationData(it.id ?: 0L, it.add1 ?: "", it.add2 ?: "") }
    }
}
