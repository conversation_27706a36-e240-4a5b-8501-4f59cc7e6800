package com.sd.loms.infrastructure.data.entity

import jakarta.persistence.*
import org.hibernate.annotations.Filter
import org.hibernate.annotations.FilterDef
import java.time.LocalDate

@Entity
@Table(name = "m_vehicles")
@FilterDef(name = "activeFilter")
@Filter(name = "activeFilter", condition = "deleted_at IS NULL")
@Cacheable
class VehicleEntity {

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	var id: Long? = null

	@Column(name = "group_id", nullable = false)
	var groupId: Long = 0

	@Column(name = "serial_nm")
	var serialNm: String? = null

	var number: String? = null

	@Column(name = "number_plate")
	var numberPlate: String? = null

	@Column(name = "belong_flg")
	var belongFlg: Boolean = true

	@Column(name = "vehicle_type")
	var vehicleType: Int? = null

	@Column(name = "tank_type")
	var tankType: String? = null

	@Column(name = "tank_limit1")
	var tankLimit1: String? = null

	@Column(name = "tank_limit2")
	var tankLimit2: String? = null

	@Column(name = "total_limit")
	var totalLimit: String? = null

	var name: String? = null

	var model: String? = null

	@Column(name = "initial_ym")
	var initialYm: String? = null

	@Column(name = "inspect_ym")
	var inspectYm: String? = null

	var remarks: String? = null

	@Column(name = "tank_size1")
	var tankSize1: String? = null

	@Column(name = "tank_size2")
	var tankSize2: String? = null

	var gas: String? = null

	@Column(name = "display_order")
	var displayOrder: Int? = null

	@Column(name = "vehicle_category")
	var vehicleCategoryId: Long? = null

	@Column(name = "weight_category")
	var weightCategoryId: Long? = null

	@Column(name = "tank_material")
	var tankMaterialId: Long? = null

	var alkaline: Long? = null

	@Column(name = "maximum_load_capacity")
	var maximumLoadCapacity: Int? = null

	@Column(name = "front_tank_capacity")
	var frontTankCapacity: Int? = null

	@Column(name = "rear_tank_capacity")
	var rearTankCapacity: Int? = null

	@Column(name = "total_tank_capacity")
	var totalTankCapacity: Int? = null

	@Column(name = "roms_id")
	var romsId: Long? = null

	@Column(name = "created_at", nullable = false)
	var createdAt: Long = 0

	@Column(name = "created_by")
	var createdBy: Long? = null

	@Column(name = "updated_at")
	var updatedAt: Long? = null

	@Column(name = "updated_by")
	var updatedBy: Long? = null

	@Column(name = "deleted_at")
	var deletedAt: LocalDate? = null

	// Relationships
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "group_id", insertable = false, updatable = false)
	var group: GroupEntity? = null

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "vehicle_category", insertable = false, updatable = false)
	var vehicleCategory: VehicleCategoryEntity? = null

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "weight_category", insertable = false, updatable = false)
	var weightCategory: VehicleWeightCategoryEntity? = null

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "tank_material", insertable = false, updatable = false)
	var tankMaterial: VehicleTankMaterialEntity? = null

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "alkaline", insertable = false, updatable = false)
	var alkalineEntity: VehicleAlkalineEntity? = null

	@OneToMany(mappedBy = "vehicle", cascade = [CascadeType.ALL], fetch = FetchType.LAZY)
	var drivers: MutableList<DriverEntity> = mutableListOf()

	@OneToMany(mappedBy = "vehicle", cascade = [CascadeType.ALL], fetch = FetchType.LAZY)
	var solutionDetails: MutableList<SolutionDetailsEntity> = mutableListOf()

	@ManyToMany(fetch = FetchType.LAZY)
	@JoinTable(
		name = "vehicle_material_categories",
		joinColumns = [JoinColumn(name = "vehicle_id")],
		inverseJoinColumns = [JoinColumn(name = "item_id")]
	)
	var materialCategories: MutableList<ItemEntity> = mutableListOf()

	@ManyToMany(mappedBy = "vehicles", fetch = FetchType.LAZY)
	var driversCanDrive: MutableList<DriverEntity> = mutableListOf()

	fun toDomain() = com.sd.loms.domain.Vehicle(
		id = id!!,
		name = name ?: serialNm ?: "",
		color = "", // No color field in entity, could be derived from other fields
		weightCapacity = maximumLoadCapacity ?: 0,
		totalTankCapacity = totalTankCapacity ?: 0,
		frontTankCapacity = frontTankCapacity ?: 0,
		rearTankCapacity = rearTankCapacity ?: 0
	)
}
