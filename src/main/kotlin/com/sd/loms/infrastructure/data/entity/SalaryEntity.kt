package com.sd.loms.infrastructure.data.entity

import jakarta.persistence.*
import org.hibernate.annotations.Filter
import org.hibernate.annotations.FilterDef
import java.time.LocalDate

@Entity
@Table(name = "m_salary", indexes = [
	Index(name = "m_salary_idx1", columnList = "g_1"),
	Index(name = "m_salary_idx2", columnList = "g_2"),
	Index(name = "m_salary_idx3", columnList = "g_3"),
	Index(name = "m_salary_idx4", columnList = "g_4"),
	Index(name = "m_salary_idx5", columnList = "g_5"),
	Index(name = "m_salary_idx6", columnList = "deleted_at")
])
@FilterDef(name = "activeFilter")
@Filter(name = "activeFilter", condition = "deleted_at IS NULL")
@Cacheable
class SalaryEntity {

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	var id: Long? = null

	@Column(name = "class", nullable = false)
	var clazz: Int = 0

	@Column(nullable = false)
	var rank: Int = 0

	@Column(nullable = false)
	var base: Int = 0

	@Column(name = "g_1")
	var g1: Boolean = false

	@Column(name = "g_2")
	var g2: Boolean = false

	@Column(name = "g_3")
	var g3: Boolean = false

	@Column(name = "g_4")
	var g4: Boolean = false

	@Column(name = "g_5")
	var g5: Boolean = false

	@Column(name = "roms_id")
	var romsId: Long? = null

	@Column(name = "created_at", nullable = false)
	var createdAt: Long = 0

	@Column(name = "created_by")
	var createdBy: Long? = null

	@Column(name = "updated_at")
	var updatedAt: Long? = null

	@Column(name = "updated_by")
	var updatedBy: Long? = null

	@Column(name = "deleted_at")
	var deletedAt: LocalDate? = null

	// Relationships
	@OneToMany(mappedBy = "salary", cascade = [CascadeType.ALL], fetch = FetchType.LAZY)
	var drivers: MutableList<DriverEntity> = mutableListOf()
}
