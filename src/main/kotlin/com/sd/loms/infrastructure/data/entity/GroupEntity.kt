package com.sd.loms.infrastructure.data.entity

import jakarta.persistence.*
import org.hibernate.annotations.Filter
import org.hibernate.annotations.FilterDef
import java.time.LocalDate

@Entity
@Table(name = "m_group", indexes = [
	Index(name = "idx_m_group_group_type", columnList = "group_type"),
	Index(name = "idx_m_group_item_cat", columnList = "item_cat"),
	Index(name = "idx_m_group_deleted_at", columnList = "deleted_at")
])
@FilterDef(name = "activeFilter")
@Filter(name = "activeFilter", condition = "deleted_at IS NULL")
@Cacheable
class GroupEntity {

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	var id: Long? = null

	@Column(nullable = false)
	lateinit var nm: String

	@Column(name = "group_nm")
	var groupNm: String? = null

	@Column(name = "group_kana")
	var groupKana: String? = null

	@Column(name = "group_type")
	var groupType: Int? = null

	@Column(name = "item_cat")
	var itemCat: String? = null

	@Column(name = "location_id")
	var locationId: Long? = null

	var opt1: String? = null

	var opt2: String? = null

	@Column(name = "roms_id")
	var romsId: Long? = null

	@Column(name = "created_at", nullable = false)
	var createdAt: Long = 0

	@Column(name = "created_by")
	var createdBy: Long? = null

	@Column(name = "updated_at")
	var updatedAt: Long? = null

	@Column(name = "updated_by")
	var updatedBy: Long? = null

	@Column(name = "deleted_at")
	var deletedAt: LocalDate? = null

	// Relationships
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "location_id", insertable = false, updatable = false)
	var location: LocationEntity? = null

	@OneToMany(mappedBy = "group", cascade = [CascadeType.ALL], fetch = FetchType.LAZY)
	var drivers: MutableList<DriverEntity> = mutableListOf()

	@OneToMany(mappedBy = "group", cascade = [CascadeType.ALL], fetch = FetchType.LAZY)
	var vehicles: MutableList<VehicleEntity> = mutableListOf()

	@OneToMany(mappedBy = "group", cascade = [CascadeType.ALL], fetch = FetchType.LAZY)
	var destGroups: MutableList<DestGroupEntity> = mutableListOf()

	@ManyToMany(mappedBy = "groups", fetch = FetchType.LAZY)
	var pickups: MutableList<PickupEntity> = mutableListOf()

	data class GroupDto(
		val id: Long,
		val nm: String,
		val groupNm: String?,
		val groupKana: String?,
		val groupType: Int?,
		val itemCat: String?,
		val locationId: Long?,
		val opt1: String?,
		val opt2: String?,
		val romsId: Long?
	)

	fun toDomain() = GroupDto(
		id = id!!,
		nm = nm,
		groupNm = groupNm,
		groupKana = groupKana,
		groupType = groupType,
		itemCat = itemCat,
		locationId = locationId,
		opt1 = opt1,
		opt2 = opt2,
		romsId = romsId
	)
}
