package com.sd.loms.infrastructure.data.entity

import jakarta.persistence.*
import org.hibernate.annotations.Filter
import org.hibernate.annotations.FilterDef
import java.time.LocalDate

@Entity
@Table(name = "m_dest_area", indexes = [
	Index(name = "idx_m_dest_area_deleted_at", columnList = "deleted_at"),
	Index(name = "u_idx_m_dest_area_company_id_serial_no", columnList = "dest_company_id, serial_no", unique = true)
])
@FilterDef(name = "activeFilter")
@Filter(name = "activeFilter", condition = "deleted_at IS NULL")
@Cacheable
class DestAreaEntity {

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	var id: Long? = null

	@Column(name = "dest_company_id", nullable = false)
	var destCompanyId: Long = 0

	@Column(name = "serial_no")
	var serialNo: Int = 1

	var nm: String? = null

	var kana: String? = null

	var opt1: String? = null

	var opt2: String? = null

	var opt3: String? = null

	@Column(name = "location_id", nullable = false)
	var locationId: Long = 0

	@Column(name = "roms_id")
	var romsId: Long? = null

	@Column(name = "created_at", nullable = false)
	var createdAt: Long = 0

	@Column(name = "created_by")
	var createdBy: Long? = null

	@Column(name = "updated_at")
	var updatedAt: Long? = null

	@Column(name = "updated_by")
	var updatedBy: Long? = null

	@Column(name = "deleted_at")
	var deletedAt: LocalDate? = null

	// Relationships
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "dest_company_id", insertable = false, updatable = false)
	var destCompany: DestCompanyEntity? = null

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "location_id", insertable = false, updatable = false)
	var location: LocationEntity? = null

	@OneToMany(mappedBy = "destArea", cascade = [CascadeType.ALL], fetch = FetchType.LAZY)
	var destSpots: MutableList<DestSpotEntity> = mutableListOf()
}
