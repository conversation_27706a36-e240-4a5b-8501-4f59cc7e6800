package com.sd.loms.infrastructure.data.repo

import com.sd.loms.infrastructure.data.EnableSoftDeleteFilter
import com.sd.loms.infrastructure.data.entity.VehicleAlkalineEntity
import io.quarkus.hibernate.orm.panache.kotlin.PanacheRepository
import jakarta.enterprise.context.ApplicationScoped

@ApplicationScoped
@EnableSoftDeleteFilter
class VehicleAlkalineRepository : PanacheRepository<VehicleAlkalineEntity> {
}
