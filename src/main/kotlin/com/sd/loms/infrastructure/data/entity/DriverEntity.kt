package com.sd.loms.infrastructure.data.entity

import jakarta.persistence.*
import org.hibernate.annotations.Filter
import org.hibernate.annotations.FilterDef
import java.time.LocalDate

@Entity
@Table(name = "m_drivers", indexes = [
	Index(name = "m_drivers_idx1", columnList = "employee_cd"),
	Index(name = "m_drivers_idx2", columnList = "deleted_at")
])
@FilterDef(name = "activeFilter")
@Filter(name = "activeFilter", condition = "deleted_at IS NULL")
@Cacheable
class DriverEntity {

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	var id: Long? = null

	@Column(name = "employee_cd")
	var employeeCd: String? = null

	@Column(name = "group_id", nullable = false)
	var groupId: Long = 0

	var nm1: String? = null

	var nm2: String? = null

	var kana2: String? = null

	var snm: String? = null

	@Column(name = "job_type")
	var jobType: Int? = null

	@Column(name = "belong_flg")
	var belongFlg: Boolean = true

	@Column(name = "salary_id")
	var salaryId: Long? = null

	var keisu: Double? = null

	@Column(name = "vehicle_id")
	var vehicleId: Long? = null

	@Column(name = "location_id", nullable = false)
	var locationId: Long = 0

	var mobile: String? = null

	@Column(name = "cmt_job")
	var cmtJob: String? = null

	var remarks: String? = null

	@Column(name = "employed_date")
	var employedDate: Long? = null

	@Column(name = "quit_flg")
	var quitFlg: Boolean = false

	@Column(name = "quit_date")
	var quitDate: Long? = null

	var opt1: String? = null

	var opt2: String? = null

	var opt3: String? = null

	@Column(name = "roms_id")
	var romsId: Long? = null

	@Column(name = "created_at", nullable = false)
	var createdAt: Long = 0

	@Column(name = "created_by")
	var createdBy: Long? = null

	@Column(name = "updated_at")
	var updatedAt: Long? = null

	@Column(name = "updated_by")
	var updatedBy: Long? = null

	@Column(name = "deleted_at")
	var deletedAt: LocalDate? = null

	// Relationships
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "group_id", insertable = false, updatable = false)
	var group: GroupEntity? = null

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "salary_id", insertable = false, updatable = false)
	var salary: SalaryEntity? = null

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "vehicle_id", insertable = false, updatable = false)
	var vehicle: VehicleEntity? = null

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "location_id", insertable = false, updatable = false)
	var location: LocationEntity? = null

	@OneToMany(mappedBy = "driver", cascade = [CascadeType.ALL], fetch = FetchType.LAZY)
	var solutionDetails: MutableList<SolutionDetailsEntity> = mutableListOf()

	@ManyToMany(fetch = FetchType.LAZY)
	@JoinTable(
		name = "driver_material_categories",
		joinColumns = [JoinColumn(name = "driver_id")],
		inverseJoinColumns = [JoinColumn(name = "item_id")]
	)
	var materialCategories: MutableList<ItemEntity> = mutableListOf()

	@ManyToMany(fetch = FetchType.LAZY)
	@JoinTable(
		name = "driver_items",
		joinColumns = [JoinColumn(name = "driver_id")],
		inverseJoinColumns = [JoinColumn(name = "item_id")]
	)
	var items: MutableList<ItemEntity> = mutableListOf()

	@ManyToMany(fetch = FetchType.LAZY)
	@JoinTable(
		name = "driver_vehicles",
		joinColumns = [JoinColumn(name = "driver_id")],
		inverseJoinColumns = [JoinColumn(name = "vehicle_id")]
	)
	var vehicles: MutableList<VehicleEntity> = mutableListOf()

	fun toDomain() = com.sd.loms.domain.DriverDto(
		id = id!!,
		name = nm1 ?: nm2 ?: snm ?: "",
		primaryVehicle = vehicleId ?: 0L,
		canHandle = materialCategories.map { it.id!! }.toSet(),
		canDrive = vehicles.map { it.id!! }.toSet()
	)
}
