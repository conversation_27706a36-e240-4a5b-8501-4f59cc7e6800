package com.sd.loms.infrastructure.data.adapter

import com.sd.loms.domain.Vehicle
import com.sd.loms.domain.Item
import com.sd.loms.domain.ports.VehicleDataAccessPort
import com.sd.loms.presentation.dto.GroupData
import com.sd.loms.infrastructure.data.EnableSoftDeleteFilter
import com.sd.loms.infrastructure.data.repo.VehicleRepository
import com.sd.loms.infrastructure.data.repo.GroupRepository
import com.sd.loms.infrastructure.data.repo.ItemRepository
import jakarta.enterprise.context.ApplicationScoped
import jakarta.transaction.Transactional

@ApplicationScoped
@Transactional
@EnableSoftDeleteFilter
class VehicleDataAccessAdapter(
    private val vehicleRepository: VehicleRepository,
    private val groupRepository: GroupRepository,
    private val itemRepository: ItemRepository
) : VehicleDataAccessPort {

    override fun getAllVehicles(): List<Vehicle> {
        return vehicleRepository.listAll().map { it.toDomain() }
    }

    override fun getVehicleById(id: Long): Vehicle? {
        return vehicleRepository.findById(id)?.toDomain()
    }

    override fun saveVehicle(vehicle: Vehicle): Long {
        val entity = vehicleRepository.findById(vehicle.id) ?: com.sd.loms.infrastructure.data.entity.VehicleEntity()
        // Map domain to entity
        entity.name = vehicle.name
        entity.maximumLoadCapacity = vehicle.weightCapacity
        entity.totalTankCapacity = vehicle.totalTankCapacity
        entity.frontTankCapacity = vehicle.frontTankCapacity
        entity.rearTankCapacity = vehicle.rearTankCapacity
        entity.createdAt = System.currentTimeMillis()

        vehicleRepository.persist(entity)
        return entity.id!!
    }

    override fun updateVehicle(vehicle: Vehicle): Vehicle {
        val entity = vehicleRepository.findById(vehicle.id) ?: throw RuntimeException("Vehicle not found")
        // Map domain to entity
        entity.name = vehicle.name
        entity.maximumLoadCapacity = vehicle.weightCapacity
        entity.totalTankCapacity = vehicle.totalTankCapacity
        entity.frontTankCapacity = vehicle.frontTankCapacity
        entity.rearTankCapacity = vehicle.rearTankCapacity
        entity.updatedAt = System.currentTimeMillis()

        vehicleRepository.persist(entity)
        return entity.toDomain()
    }

    override fun deleteVehicle(id: Long) {
        val entity = vehicleRepository.findById(id) ?: throw RuntimeException("Vehicle not found")
        vehicleRepository.delete(entity)
    }

    override fun getAllGroups(): List<GroupData> {
        return groupRepository.listAll().map { GroupData(it.id ?: 0L, it.nm ?: "") }
    }

    override fun getAllItems(): List<Item> {
        return itemRepository.listAll().map { it.toDomain() }
    }
}
