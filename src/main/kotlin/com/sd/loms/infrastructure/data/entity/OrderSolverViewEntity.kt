package com.sd.loms.infrastructure.data.entity

import jakarta.persistence.*
import org.hibernate.annotations.Immutable

/**
 * Embedded class representing location data
 */
@Embeddable
class LocationDto {
    @Column(name = "location_id")
    var id: Long = 0

    @Column(name = "location_unique_id")
    var uniqueId: String = ""

    @Column(name = "location_name")
    var name: String = ""

    @Column(name = "location_short_name")
    var shortName: String = ""

    @Column(name = "location_lat")
    var lat: String = ""

    @Column(name = "location_lon")
    var lon: String = ""

    @Column(name = "location_address")
    var address: String = ""

    // Computed properties for type conversion
    val latDouble: Double
        get() = lat.toDoubleOrNull() ?: 0.0

    val lonDouble: Double
        get() = lon.toDoubleOrNull() ?: 0.0
}

/**
 * Embedded class representing item/material data
 */
@Embeddable
class ProblemItem {
    @Column(name = "item_id")
    var id: Long = 0

    @Column(name = "item_name")
    var name: String = ""

    @Column(name = "item_cleaning_flag")
    var cleaningFlagInt: Int = 0

    // Computed property for cleaning flag
    val cleaningFlag: Boolean
        get() = cleaningFlagInt == 1
}

/**
 * Entity representing the v_orders_4_solver view with nested/embedded objects.
 * This view provides comprehensive order data for the solver optimization engine.
 */
@Entity
@Immutable
@Table(name = "v_orders_4_solver")
class OrderSolverViewEntity {

    @Id
    @Column(name = "id")
    var id: Long = 0

    @Column(name = "pickup_id")
    var pickupId: Long = 0

    @Column(name = "destination_id")
    var destinationId: Long = 0

    @Column(name = "destination_cd")
    var destinationCd: String = ""

    @Column(name = "shipper_id")
    var shipperId: Long = 0

    @Column(name = "shipper_name")
    var shipperName: String = ""

    @Column(name = "set_time")
    var setTime: String = ""

    @Column(name = "fulfill_date")
    var fulfillDate: Long = 0

    @Column(name = "volume1")
    var volume1String: String = ""

    @Column(name = "unit1")
    var unit1: Int = 0

    @Column(name = "volume2")
    var volume2String: String = ""

    @Column(name = "unit2")
    var unit2: Int = 0

    // Embedded pickup location
    @Embedded
    @AttributeOverrides(
        AttributeOverride(name = "id", column = Column(name = "pickup_location_id")),
        AttributeOverride(name = "uniqueId", column = Column(name = "pickup_location_unique_id")),
        AttributeOverride(name = "name", column = Column(name = "pickup_location_name")),
        AttributeOverride(name = "shortName", column = Column(name = "pickup_location_short_name")),
        AttributeOverride(name = "lat", column = Column(name = "pickup_location_lat")),
        AttributeOverride(name = "lon", column = Column(name = "pickup_location_lon")),
        AttributeOverride(name = "address", column = Column(name = "pickup_location_address"))
    )
    var pickupLocation: LocationDto = LocationDto()

    // Embedded destination location
    @Embedded
    @AttributeOverrides(
        AttributeOverride(name = "id", column = Column(name = "destination_location_id")),
        AttributeOverride(name = "uniqueId", column = Column(name = "destination_location_unique_id")),
        AttributeOverride(name = "name", column = Column(name = "destination_location_name")),
        AttributeOverride(name = "shortName", column = Column(name = "destination_location_short_name")),
        AttributeOverride(name = "lat", column = Column(name = "destination_location_lat")),
        AttributeOverride(name = "lon", column = Column(name = "destination_location_lon")),
        AttributeOverride(name = "address", column = Column(name = "destination_location_address"))
    )
    var destinationLocation: LocationDto = LocationDto()

    // Embedded item
    @Embedded
    @AttributeOverrides(
        AttributeOverride(name = "id", column = Column(name = "item_id")),
        AttributeOverride(name = "name", column = Column(name = "item_name")),
        AttributeOverride(name = "cleaningFlagInt", column = Column(name = "item_cleaning_flag"))
    )
    var item: ProblemItem = ProblemItem()

    // Computed properties for volume conversion
    val volume1: Int
        get() = volume1String.toIntOrNull() ?: 0

    val volume2: Int
        get() = volume2String.toIntOrNull() ?: 0
}
