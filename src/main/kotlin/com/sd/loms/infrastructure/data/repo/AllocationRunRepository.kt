package com.sd.loms.infrastructure.data.repo

import com.sd.loms.infrastructure.data.EnableSoftDeleteFilter
import com.sd.loms.infrastructure.data.entity.AllocationRunEntity
import io.quarkus.hibernate.orm.panache.kotlin.PanacheRepository
import io.quarkus.panache.common.Sort
import jakarta.enterprise.context.ApplicationScoped


@ApplicationScoped
@EnableSoftDeleteFilter
class AllocationRunRepository : PanacheRepository<AllocationRunEntity> {

    fun findByFulfillDateOrderByIdDesc(date: Long): List<AllocationRunEntity> {
        return list("fulfillDate = ?1", Sort.by("id").descending(), date)
    }
}
