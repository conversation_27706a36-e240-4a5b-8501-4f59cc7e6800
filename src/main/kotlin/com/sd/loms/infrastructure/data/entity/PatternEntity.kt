package com.sd.loms.infrastructure.data.entity

import jakarta.persistence.*
import org.hibernate.annotations.Filter
import org.hibernate.annotations.FilterDef
import java.time.LocalDate

@Entity
@Table(name = "m_patterns")
@FilterDef(name = "activeFilter")
@Filter(name = "activeFilter", condition = "deleted_at IS NULL")
@IdClass(PatternEntityId::class)
class PatternEntity {

	@Id
	@Column(name = "item_id", nullable = false)
	var itemId: Long = 0

	@Id
	@Column(name = "shipper_id", nullable = false)
	var shipperId: Long = 0

	@Id
	@Column(name = "pickup_id", nullable = false)
	var pickupId: Long = 0

	@Id
	@Column(name = "delivery_id", nullable = false)
	var deliveryId: Long = 0

	@Id
	@Column(name = "driver_id", nullable = false)
	var driverId: Long = 0

	@Id
	@Column(name = "vehicle_id", nullable = false)
	var vehicleId: Long = 0

	@Column(name = "created_at", nullable = false)
	var createdAt: Long = 0

	@Column(name = "created_by")
	var createdBy: Long? = null

	@Column(name = "updated_at")
	var updatedAt: Long? = null

	@Column(name = "updated_by")
	var updatedBy: Long? = null

	@Column(name = "deleted_at")
	var deletedAt: LocalDate? = null

	// Relationships
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "item_id", insertable = false, updatable = false)
	var item: ItemEntity? = null

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "shipper_id", insertable = false, updatable = false)
	var shipper: ShipperEntity? = null

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "pickup_id", insertable = false, updatable = false)
	var pickupLocation: LocationEntity? = null

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "delivery_id", insertable = false, updatable = false)
	var deliveryLocation: LocationEntity? = null

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "driver_id", insertable = false, updatable = false)
	var driver: DriverEntity? = null

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "vehicle_id", insertable = false, updatable = false)
	var vehicle: VehicleEntity? = null
}

@Embeddable
data class PatternEntityId(
	var itemId: Long = 0,
	var shipperId: Long = 0,
	var pickupId: Long = 0,
	var deliveryId: Long = 0,
	var driverId: Long = 0,
	var vehicleId: Long = 0
) : java.io.Serializable
