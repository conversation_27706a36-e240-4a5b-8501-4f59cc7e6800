package com.sd.loms.infrastructure.data.entity

import jakarta.persistence.*
import org.hibernate.annotations.Filter
import org.hibernate.annotations.FilterDef
import java.time.LocalDate

@Entity
@Table(name = "m_distances")
@FilterDef(name = "activeFilter")
@Filter(name = "activeFilter", condition = "deleted_at IS NULL")
@IdClass(DistanceEntityId::class)
class DistanceEntity {

	@Id
	@Column(name = "from_loc_id", nullable = false)
	var fromLocId: Long = 0

	@Id
	@Column(name = "to_loc_id", nullable = false)
	var toLocId: Long = 0

	@Column(nullable = false)
	var duration: Int = 0

	@Column(name = "created_at", nullable = false)
	var createdAt: Long = 0

	@Column(name = "created_by")
	var createdBy: Long? = null

	@Column(name = "updated_at")
	var updatedAt: Long? = null

	@Column(name = "updated_by")
	var updatedBy: Long? = null

	@Column(name = "deleted_at")
	var deletedAt: LocalDate? = null

	// Relationships
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "from_loc_id", insertable = false, updatable = false)
	var fromLocation: LocationEntity? = null

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "to_loc_id", insertable = false, updatable = false)
	var toLocation: LocationEntity? = null
}

@Embeddable
data class DistanceEntityId(
	var fromLocId: Long = 0,
	var toLocId: Long = 0
) : java.io.Serializable
