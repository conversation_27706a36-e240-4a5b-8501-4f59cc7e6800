package com.sd.loms.infrastructure.data.repo

import com.sd.loms.infrastructure.data.EnableSoftDeleteFilter
import com.sd.loms.infrastructure.data.entity.OrderEntity
import io.quarkus.hibernate.orm.panache.kotlin.PanacheRepository
import jakarta.enterprise.context.ApplicationScoped
import jakarta.persistence.EntityManager
import java.time.LocalDate

@ApplicationScoped
@EnableSoftDeleteFilter
class OrderRepository : PanacheRepository<OrderEntity> {
	fun findAllByFulfillDate(date: Long) = list("deliDate = ?1 and destGroup.groupId = 1", date)
}
