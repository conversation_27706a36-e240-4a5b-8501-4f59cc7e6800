package com.sd.loms.infrastructure.data.entity

import jakarta.persistence.*
import org.hibernate.annotations.Filter
import org.hibernate.annotations.FilterDef
import java.time.LocalDate

@Entity
@Table(name = "m_dest_company", indexes = [
	Index(name = "idx_m_dest_company_deleted_at", columnList = "deleted_at")
])
@FilterDef(name = "activeFilter")
@Filter(name = "activeFilter", condition = "deleted_at IS NULL")
@Cacheable
class DestCompanyEntity {

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	var id: Long? = null

	@Column(nullable = false)
	lateinit var name: String

	var kana: String? = null

	var opt1: String? = null

	var opt2: String? = null

	var opt3: String? = null

	@Column(name = "roms_id")
	var romsId: Long? = null

	@Column(name = "created_at", nullable = false)
	var createdAt: Long = 0

	@Column(name = "created_by")
	var createdBy: Long? = null

	@Column(name = "updated_at")
	var updatedAt: Long? = null

	@Column(name = "updated_by")
	var updatedBy: Long? = null

	@Column(name = "deleted_at")
	var deletedAt: LocalDate? = null

	// Relationships
	@OneToMany(mappedBy = "destCompany", cascade = [CascadeType.ALL], fetch = FetchType.LAZY)
	var destAreas: MutableList<DestAreaEntity> = mutableListOf()
}
