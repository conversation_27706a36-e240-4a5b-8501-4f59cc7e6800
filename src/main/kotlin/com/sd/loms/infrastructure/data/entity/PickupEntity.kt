package com.sd.loms.infrastructure.data.entity

import jakarta.persistence.*
import org.hibernate.annotations.Filter
import org.hibernate.annotations.FilterDef
import java.time.LocalDate

@Entity
@Table(name = "m_pickups", indexes = [
	Index(name = "idx_m_pickup_deleted_at", columnList = "deleted_at")
])
@FilterDef(name = "activeFilter")
@Filter(name = "activeFilter", condition = "deleted_at IS NULL")
@Cacheable
class PickupEntity {

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	var id: Long? = null

	var nm: String? = null

	var kana: String? = null

	var snm1: String? = null

	var snm2: String? = null

	@Column(name = "location_id", nullable = false)
	var locationId: Long = 0

	@Column(name = "start_time")
	var startTime: String? = null

	@Column(name = "lunch_time")
	var lunchTime: String? = null

	@Column(name = "end_time")
	var endTime: String? = null

	var opt1: String? = null

	var opt2: String? = null

	var opt3: String? = null

	@Column(name = "roms_id")
	var romsId: Long? = null

	@Column(name = "created_at", nullable = false)
	var createdAt: Long = 0

	@Column(name = "created_by")
	var createdBy: Long? = null

	@Column(name = "updated_at")
	var updatedAt: Long? = null

	@Column(name = "updated_by")
	var updatedBy: Long? = null

	@Column(name = "deleted_at")
	var deletedAt: LocalDate? = null

	// Relationships
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "location_id", insertable = false, updatable = false)
	var location: LocationEntity? = null

	@OneToMany(mappedBy = "pickup", cascade = [CascadeType.ALL], fetch = FetchType.LAZY)
	var orders: MutableList<OrderEntity> = mutableListOf()

	@ManyToMany(fetch = FetchType.LAZY)
	@JoinTable(
		name = "m_pickup_group",
		joinColumns = [JoinColumn(name = "pickup_id")],
		inverseJoinColumns = [JoinColumn(name = "group_id")]
	)
	var groups: MutableList<GroupEntity> = mutableListOf()

	data class PickupDto(
		val id: Long,
		val nm: String?,
		val kana: String?,
		val snm1: String?,
		val snm2: String?,
		val locationId: Long,
		val startTime: String?,
		val lunchTime: String?,
		val endTime: String?,
		val opt1: String?,
		val opt2: String?,
		val opt3: String?,
		val romsId: Long?
	)

	fun toDomain() = PickupDto(
		id = id!!,
		nm = nm,
		kana = kana,
		snm1 = snm1,
		snm2 = snm2,
		locationId = locationId,
		startTime = startTime,
		lunchTime = lunchTime,
		endTime = endTime,
		opt1 = opt1,
		opt2 = opt2,
		opt3 = opt3,
		romsId = romsId
	)
}
