package com.sd.loms.infrastructure.data.entity

import jakarta.persistence.*
import java.time.LocalDateTime

@Entity
@Table(name = "password_tokens")
class PasswordTokenEntity {

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	var id: Long? = null

	@Column(nullable = false)
	lateinit var hash: String

	@Column(name = "created_at", nullable = false)
	lateinit var createdAt: LocalDateTime

	@Column(name = "password_token_user", nullable = false)
	var passwordTokenUser: Long = 0

	// Relationships
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "password_token_user", insertable = false, updatable = false)
	var user: UserEntity? = null
}
