package com.sd.loms.infrastructure.data.entity

import jakarta.persistence.*
import org.hibernate.annotations.Filter
import org.hibernate.annotations.FilterDef
import java.time.LocalDate

@Entity
@Table(name = "m_pref")
@FilterDef(name = "activeFilter")
@Filter(name = "activeFilter", condition = "deleted_at IS NULL")
@Cacheable
class PrefEntity {

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	var id: Long? = null

	var name: String? = null

	@Column(name = "display_order")
	var displayOrder: Int? = null

	@Column(name = "roms_id")
	var romsId: Long? = null

	@Column(name = "created_at", nullable = false)
	var createdAt: Long = 0

	@Column(name = "created_by")
	var createdBy: Long? = null

	@Column(name = "updated_at")
	var updatedAt: Long? = null

	@Column(name = "updated_by")
	var updatedBy: Long? = null

	@Column(name = "deleted_at")
	var deletedAt: LocalDate? = null

	// Relationships
	@OneToMany(mappedBy = "pref", cascade = [CascadeType.ALL], fetch = FetchType.LAZY)
	var locations: MutableList<LocationEntity> = mutableListOf()

	data class PrefDto(
		val id: Long,
		val name: String?,
		val displayOrder: Int?,
		val romsId: Long?
	)

	fun toDomain() = PrefDto(
		id = id!!,
		name = name,
		displayOrder = displayOrder,
		romsId = romsId
	)
}
