package com.sd.loms.infrastructure.data.adapter

import com.sd.loms.domain.*
import com.sd.loms.domain.ports.SolutionDataAccessPort
import com.sd.loms.infrastructure.data.EnableSoftDeleteFilter
import com.sd.loms.infrastructure.data.entity.AllocationRunEntity
import com.sd.loms.infrastructure.data.entity.SolutionEntity
import com.sd.loms.infrastructure.data.repo.*
import jakarta.enterprise.context.ApplicationScoped
import jakarta.transaction.Transactional
import java.math.BigDecimal

@ApplicationScoped
@Transactional
@EnableSoftDeleteFilter
class SolutionDataAccessAdapter(
	private val allocationRunRepository: AllocationRunRepository,
	private val solutionRepository: SolutionRepository,
	private val vehicleRepository: VehicleRepository,
	private val driverRepository: DriverRepository,
	private val orderRepository: OrderRepository,
	private val orderSolverViewRepository: OrderSolverViewRepository,
	private val locationRepository: LocationRepository,
	private val itemRepository: ItemRepository,
	private val distanceRepository: DistanceRepository,
	private val patternRepository: PatternRepository,
	private val groupRepository: GroupRepository
) : SolutionDataAccessPort {
	override fun getAllocationRunsByDate(date: Long): List<AllocationRun> {
		val runs = allocationRunRepository.list("fulfillDate = ?1", date).map { it.toDomain() }
		return runs
	}

	override fun findFeasibleSolutions(runId: Long) =
		solutionRepository.list("runId = ?1", runId).map { it.toDomain() }

	override fun findBySolutionId(solutionId: Long): SolutionVM {
		val solution = solutionRepository.findById(solutionId)
		val allocationRun = allocationRunRepository.findById(solution?.runId ?: 0L)
		return solutionVM(allocationRun, solution)
	}

	override fun isThereAnySolverInProgress(): Boolean {
		return allocationRunRepository.count("status = ?1", RunStatus.SOLVING) > 0
	}

	override fun save(domainSolution: Solution) {
		SolutionEntity.fromDomain(domainSolution).let { solutionRepository.persist(it) }
	}

	/**
	 * Retrieves all distances where both from_loc_id and to_loc_id are in the provided list of location IDs.
	 * Excludes self-distances (where from_loc_id = to_loc_id).
	 *
	 * This is equivalent to the Go function FindAllByLocationIds in DistanceRepo.go
	 */
	fun findAllByLocationIds(locationIds: List<Long>): List<DistanceDto> {
		if (locationIds.isEmpty()) {
			throw IllegalArgumentException("Location IDs list is empty")
		}

		// Debug: Log the location IDs we're searching for
		println("DEBUG: Searching for distances with location IDs: $locationIds")

		// Use HQL/JPQL query to find distances where both endpoints are in the provided list
		// This ensures we get all bidirectional distances within our location set
		val result = distanceRepository.list(
			"((fromLocId IN ?1 AND toLocId IN ?2) OR (toLocId IN ?1 AND fromLocId IN ?2)) AND fromLocId != toLocId",
			locationIds,
			locationIds
		).map { entity ->
			DistanceDto(
				fromLoc = entity.fromLocId,
				toLoc = entity.toLocId,
				distance = entity.duration.toLong()
			)
		}

		// Debug: Log the result count
		println("DEBUG: Found ${result.size} distance records")

		return result
	}

	// Debug method to check what's in the distance table
	fun debugDistanceTable(): List<kotlin.collections.Map<String, Any?>> {
		return distanceRepository.listAll().take(10).map { entity ->
			mapOf(
				"fromLocId" to entity.fromLocId,
				"toLocId" to entity.toLocId,
				"duration" to entity.duration
			)
		}
	}

	private fun solutionVM(allocationRun: AllocationRunEntity?, solution: SolutionEntity?): SolutionVM {
		if (allocationRun == null || solution == null) {
			return SolutionVM(
				runId = 0,
				solutionId = 0,
				totalTime = 0,
				solverStatus = "NOT_FOUND",
				vehicleRoutes = emptyList(),
				vehicles = emptyList()
			)
		}

		// Get all vehicles used in the solution
		val vehicleIds = solution.solutionDetails.map { it.vehicleId }.distinct()
		val vehicles = vehicleRepository.list("id IN ?1", vehicleIds).map { it.toDomain() }

		// Group solution details by vehicle to create routes
		val vehicleRoutes = solution.solutionDetails
			.groupBy { it.vehicleId }
			.map { (vehicleId, details) ->
				val vehicle = vehicles.find { it.id == vehicleId }
				val driver = driverRepository.findById(details.first().driverId)

				// Sort visits by visit index
				val sortedDetails = details.sortedBy { it.visitIndex }

				// Create visits from solution details
				val visits = sortedDetails.map { detail ->
					val location = locationRepository.findById(detail.locationUniqueId.toLongOrNull() ?: 0L)
					val order = orderRepository.findById(detail.orderId.toLongOrNull() ?: 0L)
					val item = order?.let { itemRepository.findById(it.itemId) }

					// Determine visit type based on location or order data
					val visitType = when {
						location?.type == "DEPOT" -> VisitType.DEPOT
						detail.locationUniqueId.contains("pickup", ignoreCase = true) -> VisitType.PICKUP
						else -> VisitType.DELIVERY
					}

					VisitVM(
						id = detail.orderId.toLongOrNull() ?: 0L,
						uniqueId = detail.locationUniqueId,
						jobId = detail.orderId.toLongOrNull() ?: 0L,
						type = visitType,
						visitIndex = detail.visitIndex,
						item = item?.name ?: "",
						arrivalTime = detail.arrivalTime.toLong(),
						serviceDuration = detail.serviceTime.toLong(),
						departureTime = detail.departureTime.toLong(),
						distanceToDepot = detail.timeToDepot?.toLong(),
						distanceFromPreviousVisit = null // Would need to be calculated
					)
				}

				VehicleRouteVM(
					vehicle = VehicleVM(
						id = vehicle?.id ?: vehicleId,
						driverId = driver?.id ?: 0L,
						canHandle = vehicle?.canHandle?.map { it.name }?.toSet() ?: emptySet(),
						time = sortedDetails.sumOf { it.departureTime.toLong() - it.arrivalTime.toLong() }
					),
					visits = visits
				)
			}

		return SolutionVM(
			runId = allocationRun.id,
			solutionId = solution.id!!,
			totalTime = solution.totalTime!!,
			solverStatus = allocationRun.status.name,
			vehicleRoutes = vehicleRoutes,
			vehicles = vehicles
		)
	}



	override fun getProblemInput(date: Long, withHistory: Boolean, withEvenAllocation: Boolean): AllocationProblem {
		val jobs = getJobsByFulfillDate(date)
		if (jobs.isEmpty()) {
			throw IllegalStateException("No jobs found")
		}

		// Get depot locations
		val depotLocations = getDepotLocations()

		// Get unique location IDs from jobs
		val locationIds = mutableSetOf<Long>()
		jobs.forEach { job ->
			locationIds.add(job.pickupId)
			locationIds.add(job.deliveryId)
		}

		// Get pickup and delivery locations from database
		val orderLocations = if (locationIds.isNotEmpty()) {
			locationRepository.list("id IN ?1", locationIds.toList())
				.map { entity ->
					// Parse lat/lng from latLng string (format: "lat,lng")
					val coords = entity.latLng.split(",")
					val lat = if (coords.size >= 2) coords[0].toBigDecimalOrNull() ?: BigDecimal.ZERO else BigDecimal.ZERO
					val lon = if (coords.size >= 2) coords[1].toBigDecimalOrNull() ?: BigDecimal.ZERO else BigDecimal.ZERO

					Location(
						id = entity.id ?: 0L,
						uniqueId = entity.uniqueId ?: "",
						locationType = when (entity.type) {
							"PICKUP" -> 1
							"DELIVERY" -> 2
							else -> 0
						},
						description = listOfNotNull(entity.add1, entity.add2, entity.add3).joinToString("、"),
						address = listOfNotNull(entity.add1, entity.add2, entity.add3).joinToString("、"),
						lat = lat,
						lon = lon,
						depot = false,
						readyTime = 32400000L,
						dueTime = 70800000L,
						serviceDuration = 1200000L
					)
				}
		} else {
			emptyList()
		}

		// Combine all locations and remove duplicates
		val allLocations = (orderLocations + depotLocations).distinctBy { it.id }
		val allLocationIds = allLocations.map { it.id }

		// Get items from jobs
		val itemIds = jobs.map { it.materialCategoryId }.distinct()
		val items = if (itemIds.isNotEmpty()) {
			itemRepository.list("id IN ?1", itemIds).map { it.toDomain() }
		} else {
			emptyList()
		}

		// Get vehicles with their capabilities
		val vehicles = getVehicles(items)

		// Get drivers with their capabilities
		val drivers = getDrivers(items)

		// Get distances between locations using the Go-equivalent method
		val distances = findAllByLocationIds(allLocationIds)

		// Convert jobs to problem jobs (OrderDto)
		val problemJobs = jobs.map { job -> toProblemJob(job) }

		// Get patterns if history is enabled
		val patterns = if (withHistory) {
			getPatterns()
		} else {
			emptyList()
		}

		return AllocationProblem(
			withHistory = withHistory,
			withEvenAllocation = withEvenAllocation,
			locations = allLocations,
			vehicles = vehicles,
			drivers = drivers,
			patterns = patterns,
			distances = distances,
			orders = problemJobs,
			items = items
		)
	}

	private fun getJobsByFulfillDate(date: Long): List<JobDto> {
		val orderEntities = orderSolverViewRepository.findAllByFulfillDateAndGroupId(date, 1)

		return orderEntities.map { entity ->
			JobDto(
				id = entity.id,
				pickupId = entity.pickupLocation.id,
				deliveryId = entity.destinationLocation.id,
				materialCategoryId = entity.item.id,
				fulfillDate = entity.fulfillDate
			)
		}
	}



	private fun getDepotLocations(): List<Location> {

		val groupEntity = groupRepository.findById(1L)?: throw RuntimeException("本社 not found")
		return listOf(
			Location(
				id = groupEntity.id!!,
				uniqueId = groupEntity.location?.uniqueId?: "",
				locationType = 0, // Depot type
				description = groupEntity.groupNm?: groupEntity.nm,
				address = groupEntity.location?.address?: "",
				lat = groupEntity.location?.lat?: BigDecimal.ZERO,
				lon = groupEntity.location?.lon?: BigDecimal.ZERO,
				depot = true,
				readyTime = 32400000L, // 9:00 AM in milliseconds
				dueTime = 70800000L,   // 7:40 PM in milliseconds
				serviceDuration = 1200000L // 20 minutes in milliseconds
			)
		)
	}

	private fun toProblemPickup(location: Location): Location {
		return location.copy(
			locationType = 1, // Pickup type
			depot = false
		)
	}

	private fun toProblemDestination(location: Location): Location {
		return location.copy(
			locationType = 2, // Delivery type
			depot = false
		)
	}

	private fun toProblemJob(job: JobDto): OrderDto {
		return OrderDto(
			id = job.id,
			pickupId = job.pickupId,
			deliveryId = job.deliveryId,
			itemId = job.materialCategoryId,
			shipperId = 1L, // Default shipper
			fulfillDate = job.fulfillDate,
			weight = 0, // Default weight
			litres = 0  // Default litres
		)
	}



	private fun getVehicles(items: List<Item>): List<VehicleDto> {
		val vehicles = vehicleRepository.listAll()

		return vehicles.map { entity ->
			// Get material categories that this vehicle can handle
			val canHandle = entity.materialCategories
				.mapNotNull { mc ->
					items.find { it.id == mc.id }?.let { it.id }
				}

			// Convert to problem vehicle
			val problemVehicle = entity.toDomain()
			VehicleDto(
				id = problemVehicle.id,
				name = problemVehicle.name,
				color = problemVehicle.color,
				weightCapacity = problemVehicle.weightCapacity,
				totalTankCapacity = problemVehicle.totalTankCapacity,
				frontTankCapacity = problemVehicle.frontTankCapacity,
				rearTankCapacity = problemVehicle.rearTankCapacity,
				canHandle = canHandle.toSet()
			)
		}
	}

	private fun getDrivers(items: List<Item>): List<DriverDto> {
		val drivers = driverRepository.list("groupId = ?1", 1L)

		val problemDrivers = drivers.map { entity ->
			// Get material categories that this driver can handle
			val canHandle = entity.materialCategories
				.mapNotNull { mc ->
					items.find { it.id == mc.id }?.let { it.id }
				}

			// Convert to problem driver
			val problemDriver = entity.toDomain()
			val canDrive = entity.vehicles.map { it.id ?: 0L }

			DriverDto(
				id = problemDriver.id,
				name = problemDriver.name,
				primaryVehicle = entity.vehicleId ?: 0L,
				canHandle = canHandle.toSet(),
				canDrive = canDrive.toSet()
			)
		}

		// Filter useful drivers (have primary vehicle or can handle materials)
		return problemDrivers.filter { driver ->
			val hasPrimary = driver.primaryVehicle != 0L
			val canHandleIsNotEmpty = driver.canHandle.isNotEmpty()
			hasPrimary || canHandleIsNotEmpty
		}
	}

	private fun getPatterns(): List<PatternDto> {
		val patterns = patternRepository.listAll()

		return patterns.map { entity ->
			PatternDto(
				shipperId = entity.shipperId,
				pickupId = entity.pickupId,
				deliveryId = entity.deliveryId,
				itemId = entity.itemId,
				vehicleId = entity.vehicleId,
				driverId = entity.driverId
			)
		}
	}

}
