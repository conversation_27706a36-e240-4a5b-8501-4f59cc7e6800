package com.sd.loms

import org.springframework.boot.autoconfigure.jdbc.DataSourceProperties
import org.springframework.boot.context.properties.ConfigurationProperties
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.context.annotation.Primary
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate

@Configuration
class DatasourceConfig {


    @Bean
    @ConfigurationProperties("spring.datasource.destination")
    fun destinationDataSourceProperties() = DataSourceProperties()

    @Bean
    @Primary
    fun destinationDataSource() = destinationDataSourceProperties().initializeDataSourceBuilder().build()

    @Bean
    fun destinationJdbcTemplate() = NamedParameterJdbcTemplate(destinationDataSource())


    @Bean
    @ConfigurationProperties("spring.datasource.source")
    fun sourceDataSourceProperties() = DataSourceProperties()

    @Bean
    fun sourceDataSource() = sourceDataSourceProperties().initializeDataSourceBuilder().build()

    @Bean
    fun sourceJdbcTemplate() = NamedParameterJdbcTemplate(sourceDataSource())

    @Bean
    @ConfigurationProperties("spring.datasource.google")
    fun googleDataSourceProperties() = DataSourceProperties()

    @Bean
    fun googleDataSource() = googleDataSourceProperties().initializeDataSourceBuilder().build()

    @Bean
    fun googleJdbcTemplate() = NamedParameterJdbcTemplate(googleDataSource())

}
