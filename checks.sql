-- ambiguity in destination table
select g.*, s.*, a.*, c.*
from m_dest_group g
         left join m_dest_spot s on s.ds_no = g.ds_no
         left join m_dest_area a on a.da_no = s.da_no
         left join m_dest_company c on c.dc_no = a.dc_no
where g_no = 1
  and g.delete_flg = false
  and dest_cd = '0000000516'

order by g.dg_no, g.dest_cd, s.ds_no, s.da_no, c.dc_no;

-- group by g.dest_cd having count(*) > 1;

select *
from m_dest_group
where dest_cd = '0000000516';


select distinct order_id, vehicle_cd
from t_assign_detail
where order_id in (select ad.order_id
                   from t_assign_detail ad
                            left join t_order o on ad.order_id = o.order_id
                            LEFT JOIN v_destination vd ON o.dest_cd = vd.dest_cd
                   where ad.order_id is not null
                     and vehicle_cd is not null
                     and deli_date between '2024-01-01' and '2025-01-01'
                     and g_no = 1
                     and o.order_status = 1
                     and o.delete_flg = false
                   group by ad.order_id
                   having count(distinct vehicle_cd) > 1)

  and order_id is not null
  and vehicle_cd is not null

order by order_id, vehicle_cd;


select o.order_id, type_no, import_key, dest_cd, item_cd, pickup_cd,
       coalesce(volume1, '') || p.p_value as 数量,
       coalesce(volume2, '') || p2.p_value as 数量2,
       ship_date, deli_date, set_time, cmt1,

       v.vehicle_cd, serial_nm, number, number_plate, v.belong_flg, vehicle_type,
       tank_type, tank_limit1, tank_limit2, total_limit,
       nm, model, tank_size1, tank_size2,
       a.driver_cd, d.snm
from t_order o
            left join t_assign_detail ad on o.order_id = ad.order_id
            left join t_assign a on a.a_no = ad.a_no
            left join m_drivers d on a.driver_cd = d.driver_cd
            left join m_vehicles v on ad.vehicle_cd = v.vehicle_cd
            left join m_params p on o.unit1 = p.p_id and p.p_cat = 'unit'
            left join m_params p2 on o.unit2::text = p2.p_id and p2.p_cat = 'unit'
where o.order_id in (select ad.order_id
                   from t_assign_detail ad
                            left join t_order o on ad.order_id = o.order_id
                            LEFT JOIN v_destination vd ON o.dest_cd = vd.dest_cd
                   where ad.order_id is not null
                     and vehicle_cd is not null
                     and deli_date between '2024-01-01' and '2025-01-01'
                     and g_no = 1
                     and o.order_status = 1
                     and o.delete_flg = false
                   group by ad.order_id
                   having count(distinct vehicle_cd) > 1);



create view public.v_order
            (type_no, order_type, file_nm, type_remarks, order_id, item_cd, item_nm, item_snm, item_sort, category,
             dest_cd, pickup_cd, pickup_nm, pickup_snm, volume1, unit1, volume2, unit2, requirement, ship_date,
             deli_date, set_time, cmt1, cmt2, updater_id, remarks, regist_ymd, update_ymd, delete_flg, g_no, shipper_cd,
             shipper_snm, nm, kana1, snm1, spot, add1, add2, add3, tel, fax, dest_remarks, order_status, p_no,
             lock_u_id, partner_name, cnt, driver_name)
as
SELECT tot.type_no,
       tot.order_type,
       tot.file_nm,
       tot.remarks                              AS type_remarks,
       o.order_id,
       o.item_cd,
       mi.nm                                    AS item_nm,
       mi.snm1                                  AS item_snm,
       mi.sort                                  AS item_sort,
       mi.category,
       o.dest_cd,
       o.pickup_cd,
       mp.nm                                    AS pickup_nm,
       mp.snm1                                  AS pickup_snm,
       o.volume1,
       o.unit1,
       o.volume2,
       o.unit2,
       o.requirement,
       o.ship_date,
       o.deli_date,
       o.set_time,
       o.cmt1,
       o.cmt2,
       o.updater_id,
       o.remarks,
       o.regist_ymd,
       o.update_ymd,
       o.delete_flg,
       vd.g_no,
       vd.shipper_cd,
       vd.shipper_snm,
       vd.nm,
       vd.kana1,
       vd.snm1,
       vd.spot,
       vd.add1,
       vd.add2,
       vd.add3,
       vd.tel,
       vd.fax,
       vd.remarks                               AS dest_remarks,
       o.order_status,
       o.p_no,
       o.lock_u_id,
       mpa.snm1                                 AS partner_name,
       COALESCE(count(tad.order_id), 0::bigint) AS cnt,
       array_agg(md.snm)                        AS driver_name
FROM t_order_type tot
         JOIN t_order o ON tot.type_no = o.type_no
         LEFT JOIN v_destination vd ON o.dest_cd = vd.dest_cd
         LEFT JOIN t_assign_detail tad ON tad.order_id = o.order_id AND tad.delete_flg = false
         LEFT JOIN t_assign ta ON tad.a_no = ta.a_no
         LEFT JOIN m_drivers md ON ta.driver_cd = md.driver_cd
         LEFT JOIN m_partner mpa ON o.p_no = mpa.p_no
         JOIN m_items mi ON o.item_cd = mi.item_cd
         JOIN m_pickup mp ON o.pickup_cd = mp.pickup_cd
GROUP BY tot.type_no, tot.order_type, tot.file_nm, tot.remarks, o.order_id, o.item_cd, mi.nm, mi.snm1, mi.sort,
         mi.category, o.dest_cd, o.pickup_cd, mp.nm, mp.snm1, o.volume1, o.unit1, o.volume2, o.unit2, o.requirement,
         o.ship_date, o.deli_date, o.set_time, o.cmt1, o.cmt2, o.updater_id, o.remarks, o.regist_ymd, o.update_ymd,
         o.delete_flg, vd.g_no, vd.shipper_cd, vd.shipper_snm, vd.nm, vd.kana1, vd.snm1, vd.spot, vd.add1, vd.add2,
         vd.add3, vd.tel, vd.fax, vd.remarks, o.order_status, o.p_no, o.lock_u_id, mpa.snm1;

alter table public.v_order
    owner to postgres;

