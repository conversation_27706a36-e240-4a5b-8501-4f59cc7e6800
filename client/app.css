@import "../node_modules/admin-lte/dist/css/adminlte.min.css";
@import "../node_modules/bootstrap-icons/font/bootstrap-icons.css";
@import '../node_modules/unpoly/unpoly.min.css';
@import '../node_modules/unpoly/unpoly-bootstrap5.min.css';
@import '../node_modules/tom-select/dist/css/tom-select.bootstrap5.min.css';
/* @import 'sweetalert2/dist/sweetalert2.min.css'; */

.card-transparent {
	background-color: transparent;
	border: none; /* Optionally remove border */
}
.card-transparent .card-body {
	background-color: transparent;
}


.bi {
  font-size: 18px;
}

#map {
  height: 70vh;
}

.fade-me-out.htmx-swapping {
  opacity: 0;
  transition: opacity 0.03s ease-out;
}

.htmx-indicator {
  background-color: black;
  position: fixed;
  bottom: 0;
  right: 0;
  width: 60px;
  padding: 5px 5px 5px 5px
}


.form-control.is-invalid {
  border-color: #dc3545 !important;
  padding-right: calc(1.5em + 0.75rem) !important;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 12 12' width='12' height='12' fill='none' stroke='%23dc3545'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath stroke-linejoin='round' d='M5.8 3.6h.4L6 6.5z'/%3e%3ccircle cx='6' cy='8.2' r='.6' fill='%23dc3545' stroke='none'/%3e%3c/svg%3e") !important;
  background-repeat: no-repeat !important;
  background-position: right calc(0.375em + 0.1875rem) center !important;
  background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem) !important;

  :focus {
    box-shadow: 0 0 0 0.25rem rgb(220 53 69 / 25%) !important;
  }
}

.new-fragment:not(.fragment-explainer):not(up-modal):not(up-popup) {
  transition: background-color 1s ease;
  background-color: rgba(255, 221, 0, 0);
}

.new-fragment:not(.fragment-explainer):not(up-modal):not(up-popup).inserted {
  background-color: rgba(255, 221, 0, 0.7);
}

[data-bs-theme=dark] {
  up-popup, up-cover-box, up-drawer-box, up-modal-box {
    background-color: rgb(33, 37, 41);
  }

  .ts-dropdown {
    color: var(--bs-body-color);
  }
}

up-popup, up-cover-box, up-drawer-box, up-modal-box {
  overflow: initial !important;
}

.ts-dropdown, .dropdown-input {
  box-shadow: none !important;
}

.sidebar-brand {
  height: 3.55rem !important;
  justify-content: left !important;
  padding: 22px !important;
}

.layout-navbar-fixed {
  .app-wrapper .app-header {
    left: 0;
    position: fixed;
    right: 0;
    top: 0;
    z-index: 1037;
  }

  .app-wrapper .app-main {
    margin-top: calc(3.5rem + 1px);
  }
}

.layout-footer-fixed {
  .app-wrapper .app-main {
    padding-bottom: calc(3.5rem + 1px);
  }

  .app-wrapper .app-footer {
    bottom: 0;
    left: 0;
    position: fixed;
    right: 0;
    z-index: 1032;
  }
}

/*.login-box, .register-box {
  width: 500px;
}*/

@media (min-width: 768px) {
  body:not(.sidebar-mini-md):not(.sidebar-mini-xs):not(.layout-top-nav) .app-footer,
  body:not(.sidebar-mini-md):not(.sidebar-mini-xs):not(.layout-top-nav) .app-header {
    transition: margin-left .3s ease-in-out;
    margin-left: 250px;
  }
}

@media (min-width: 992px) {
  .sidebar-mini.sidebar-collapse .app-footer,
  .sidebar-mini.sidebar-collapse .app-header {
    margin-left: 4.6rem !important;
  }
}

.ts-control, .ts-control input, .ts-dropdown {
	color: var(--bs-body-color);
}
