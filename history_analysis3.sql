select deli_date date,
       cnt,
       coalesce(set_time, 'なし') as                指定時間,
       shipper_snm as 荷主,
       pickup_nm as 積地,
       item_snm as 商品,
       coalesce(volume1, '') || p.p_value as 数量,

       coalesce(snm1, nm, '') || '(' || spot || ')' 届先,
       cmt1 社外記事,

       driver_name 乗務員
from v_order o
         left join m_params p on o.unit1 = p.p_id and p.p_cat = 'unit'
where g_no = 1

  and o.delete_flg = false

  and deli_date between '2025-02-01' and '2025-02-28'
  and order_status = 1
ORDER BY deli_date, item_cd, shipper_cd, dest_cd, order_id, cnt
;


select to_char(deli_date, 'YYYY-MM') as month,
       cnt,
       coalesce(set_time, 'なし') as                指定時間,
       shipper_snm as 荷主,
       pickup_nm as 積地,
       item_snm as 商品,
       coalesce(volume1, '') || p.p_value as 数量,

       coalesce(snm1, nm, '') || '(' || spot || ')' 届先,
       cmt1 社外記事,

       driver_name 乗務員
from v_order o
         left join m_params p on o.unit1 = p.p_id and p.p_cat = 'unit'
where g_no = 1

  and o.delete_flg = false

  and deli_date between '2024-01-01' and '2025-01-01'
  and order_status = 1
ORDER BY month, item_cd, shipper_cd, dest_cd, order_id, cnt
;


select
    order_id,
        to_char(deli_date, 'YYYY-MM') as month,
       cnt,
       coalesce(set_time, 'なし') as                指定時間,
       shipper_snm as 荷主,
       pickup_nm as 積地,
       item_snm as 商品,
       coalesce(volume1, '') || p_value as 数量,

       coalesce(snm1, nm, '') || '(' || spot || ')' 届先,
       cmt1 社外記事,

       driver_name 乗務員

from
(SELECT tot.type_no,
       tot.order_type,
       tot.file_nm,
       tot.remarks                              AS type_remarks,
       o.order_id,
       o.item_cd,
       mi.nm                                    AS item_nm,
       mi.snm1                                  AS item_snm,
       mi.sort                                  AS item_sort,
       mi.category,
       o.dest_cd,
       o.pickup_cd,
       mp.nm                                    AS pickup_nm,
       mp.snm1                                  AS pickup_snm,
       o.volume1,
       o.unit1,
       o.volume2,
       o.unit2,
       o.requirement,
       o.ship_date,
       o.deli_date,
       o.set_time,
       o.cmt1,
       o.cmt2,
       o.updater_id,
       o.remarks,
       o.regist_ymd,
       o.update_ymd,
       o.delete_flg,
       vd.g_no,
       vd.shipper_cd,
       vd.shipper_snm,
       vd.nm,
       vd.kana1,
       vd.snm1,
       vd.spot,
       vd.add1,
       vd.add2,
       vd.add3,
       vd.tel,
       vd.fax,
       vd.remarks                               AS dest_remarks,
       o.order_status,
       o.p_no,
       o.lock_u_id,
       mpa.snm1                                 AS partner_name,
       COALESCE(count(tad.order_id), 0::bigint) AS cnt,
       array_agg(md.snm)                        AS driver_name
FROM t_order_type tot
         JOIN t_order o ON tot.type_no = o.type_no
         LEFT JOIN v_destination vd ON o.dest_cd = vd.dest_cd
         LEFT JOIN t_assign_detail tad ON tad.order_id = o.order_id AND tad.delete_flg = false
         LEFT JOIN t_assign ta ON tad.a_no = ta.a_no
         LEFT JOIN m_drivers md ON ta.driver_cd = md.driver_cd
         LEFT JOIN m_partner mpa ON o.p_no = mpa.p_no
         JOIN m_items mi ON o.item_cd = mi.item_cd
         JOIN m_pickup mp ON o.pickup_cd = mp.pickup_cd
GROUP BY tot.type_no, tot.order_type, tot.file_nm, tot.remarks, o.order_id, o.item_cd, mi.nm, mi.snm1, mi.sort,
         mi.category, o.dest_cd, o.pickup_cd, mp.nm, mp.snm1, o.volume1, o.unit1, o.volume2, o.unit2, o.requirement,
         o.ship_date, o.deli_date, o.set_time, o.cmt1, o.cmt2, o.updater_id, o.remarks, o.regist_ymd, o.update_ymd,
         o.delete_flg, vd.g_no, vd.shipper_cd, vd.shipper_snm, vd.nm, vd.kana1, vd.snm1, vd.spot, vd.add1, vd.add2,
         vd.add3, vd.tel, vd.fax, vd.remarks, o.order_status, o.p_no, o.lock_u_id, mpa.snm1) o
    left join m_params p on o.unit1 = p.p_id and p.p_cat = 'unit'
where g_no = 1

  and o.delete_flg = false

  and deli_date between '2024-01-01' and '2025-01-01'
  and order_status = 1
ORDER BY month, item_cd, shipper_cd, dest_cd, order_id, cnt
;