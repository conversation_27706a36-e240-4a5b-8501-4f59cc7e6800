package drivers

import (
	. "loms/pkg/templates/tags"
	"loms/pkg/types"
)

templ Index(page *types.Page[*types.DriverForm]) {
	@AddLink(GetUrl(ctx, types.RouteDriversAddNew))
	@Table() {
		@THead() {
			@Sortable(SProps("id", "ID", "", page.Paging))
			@Sortable(SProps("Name", "乗務員", "", page.Paging))
			<th>対応可能</th>
		}
		<tbody>
			for _, entity := range page.Content {
				<tr>
					@EditLinkTd(types.RouteDriversDetails, S(entity.ID), S(entity.ID))
					@EditLinkTd(types.RouteDriversDetails, S(entity.ID), entity.Name)
					<td>{ entity.CanHandleNames }</td>
				</tr>
			}
		</tbody>
	}
	@Pagination(PProps("", page.TotalPages(), page.Paging))
}
