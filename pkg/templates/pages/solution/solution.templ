package solution

import (
	"fmt"
	. "loms/pkg/templates/tags"
	"loms/pkg/types"
)

templ SolutionPage(data *types.SolutionDetailsData) {
	<div class="mt-4">
		<div class="card" id="solution-details">
			<div class={ "card-header", templ.KV("text-bg-warning", data.Solution.ManuallyChanged) }>
				<div class="card-title">
					結果の詳細
					if data.Solution.ManuallyChanged {
						「手動で変更されました」
					}
				</div>
			</div>
			<div class="card-body">
				<div x-data="tripDataAlpine()" x-init="init()">
					<div class="mb3">
						<button class="btn btn-outline-primary" @click="showCompleteResult = !showCompleteResult">完全な結果を切り替え</button>
						if data.Solution.ManuallyChanged {
							<a class="btn btn-danger" up-confirm="本当に削除しますか?" up-method="delete" href={ GetUrl(ctx, types.RouteSolutionDelete, data.Solution.SolutionId) }>
								<i class="bi-trash"></i>
								削除
							</a>
						}
					</div>
					<div class="mb-3">
						<h3>車両</h3>
						for _, it := range data.Solution.Vehicles {
							<a class="btn btn-sm btn-outline-secondary" @click={ fmt.Sprintf("onVehicleChange(%v)", toJson(it)) }>{ it.RomsID } - { it.Driver.Name }</a>
						}
					</div>
					@Details(data)
				</div>
			</div>
		</div>
	</div>
}

templ LastTr(vehicleId int64, visit types.VisitVM) {
	<tr class={ fmt.Sprintf("v_%d", vehicleId) }>
		<td></td>
		<td class="p-0">
			<div class="card card-transparent">
				<div class="card-body">
					<h6>{ visit.Name }</h6>
					<p class="card-text mb-2 text-body-secondary">{ visit.Address }</p>
					<p class="card-text">{ ToTimeDisplay(visit.ArrivalTime, 0) }</p>
				</div>
			</div>
		</td>
	</tr>
}

templ Details(data *types.SolutionDetailsData) {
	<div id="solution">
		if data.ErrorMessages != nil && len(data.ErrorMessages) > 0 {
			<div class={ fmt.Sprintf("alert alert-%s alert-dismissible", "danger") } role="alert">
				<h4>ハード制約に失敗しました: </h4>
				<ol>
					for _, msg := range data.ErrorMessages {
						<li>{ msg }</li>
					}
				</ol>
				<button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
			</div>
		}
		<div class="py-4" id="byVehiclePanel"></div>
		<div class="row" id="vehicles_tables">
			<div class="vehicle_table col-12">
				<h3>グーグル: 合計時間： { ToTime(data.Solution.TotalTime) }</h3>
				<div x-show="showCompleteResult">
					for _, s := range data.Solution.VehicleRoutes {
						<div class="card mb-4">
							<div class="card-header">
								<h5>
									車両：{ s.Vehicle.RomsID } <!-- 時間：{ ToTime(s.Vehicle.Time) } -->
								</h5>
								<h5>ドライバー: { s.Driver.Name }</h5>
							</div>
							<div class="card-body p-0">
								<table class="table table-sm table-striped table-hover">
									<tbody>
										<tr class={ fmt.Sprintf("v_%d", s.Vehicle.ID) }>
											<td></td>
											<td class="p-0">
												<div class="card card-transparent">
													<div class="card-body">
														<h6>{ s.Visits[0].Name }</h6>
														<p class="card-text mb-2 text-body-secondary">{ s.Visits[0].Address }</p>
														<p class="card-text">{ ToTimeDisplay(s.Visits[0].ArrivalTime, 0) }</p>
													</div>
												</div>
											</td>
										</tr>
										for _, it := range s.Jobs {
											<tr class={ fmt.Sprintf("v_%d", s.Vehicle.ID) }>
												<td>{ ToJobId(it.RomsJobID) }</td>
												<td class="p-0">
													<div class="card-group">
														<div class="card card-transparent">
															<div class="card-body">
																<h6>積み地</h6>
																<p class="card-text mb-2 text-body-secondary">{ it.Pickup.Name } { it.Pickup.Address }</p>
																<p class="card-text">{ ToTimeDisplay(it.Pickup.ArrivalTime, it.Pickup.DepartureTime) }</p>
															</div>
														</div>
														<div class="card card-transparent">
															<div class="card-body">
																<h6>届け先</h6>
																<p class="card-text mb-2 text-body-secondary">{ it.Delivery.Name } { it.Delivery.Address }</p>
																<p class="card-text">{ ToTimeDisplay(it.Delivery.ArrivalTime, it.Delivery.DepartureTime) }</p>
															</div>
														</div>
													</div>
												</td>
											</tr>
										}
										@LastTr(s.Vehicle.ID, s.Visits[len(s.Visits)-1])
									</tbody>
								</table>
							</div>
						</div>
					}
				</div>
			</div>
			<div class="vehicle_table col-lg-12 col-xl-6">
				<h3>グーグル</h3>
				<table class="table table-sm">
					<thead>
						<tr>
							<th colspan="3">合計時間： { ToTime(data.Solution.TotalTime) }</th>
						</tr>
					</thead>
					<tbody>
						for _, s := range data.Solution.VehicleRoutes {
							<tr class={ fmt.Sprintf("v_%d", s.Vehicle.ID) } x-show={ fmt.Sprintf("selectedVehicle.id == %d", s.Vehicle.ID) }>
								<th>車両：{ s.Vehicle.RomsID }</th>
								<th></th>
								<th>時間：{ ToTime(s.Vehicle.Time) }</th>
							</tr>
							for _, it := range s.Visits {
								<tr class={ fmt.Sprintf("v_%d", s.Vehicle.ID) } x-show={ fmt.Sprintf("selectedVehicle.id == %d", s.Vehicle.ID) }>
									<td>{ ToJobId(it.RomsJobID) }</td>
									<td>{ it.Name }</td>
									<td>{ ToTimeDisplay(it.ArrivalTime, it.DepartureTime) }</td>
								</tr>
							}
						}
					</tbody>
				</table>
			</div>
			<div id="naviTimeResult" class="col-lg-12 col-xl-6">
				<h3>ナビタイム</h3>
				<table class="table table-sm">
					<thead>
						<tr>
							<th colspan="3">&nbsp;</th>
						</tr>
					</thead>
					<tbody>
						<tr>
							<th x-text="selectedVehicle.name ? '車両：' + selectedVehicle.name : ''"></th>
							<th></th>
							<th x-text="totalTime ? '時間：' + totalTime : ''"></th>
						</tr>
						<template x-for="visit in visits" :key="visit.id">
							<tr>
								<td></td>
								<td x-text="visit.name"></td>
								<td x-text="visit.arrivalTime + ((visit.departureTime == '00:00' ) ? '': (' 〜 ' + visit.departureTime))"></td>
							</tr>
						</template>
					</tbody>
				</table>
			</div>
		</div>
		<i id="data" up-data={ data.Solution.Data }></i>
		<i id="solution-info" data-date={ data.Solution.Date } data-solution-id={ S(data.Solution.SolutionId) }></i>
	</div>
}

func ToJobId(jobId string) string {
	if jobId == "" {
		return ""
	}
	return fmt.Sprintf("受注：%s", jobId)
}

func ToTimeDisplay(arrivalTime, departureTime int64) string {
	if arrivalTime == 0 {
		return ""
	}
	if departureTime == 0 {
		return fmt.Sprintf("%s", ToTime(arrivalTime))
	}
	return fmt.Sprintf("%s 〜 %s", ToTime(arrivalTime), ToTime(departureTime))
}

func toJson(vehicle types.VehicleVM) string {
	return fmt.Sprintf(`{ "id": %v, "name": "%v" }`, vehicle.ID, vehicle.Name)
}
